import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, Trash2, Pencil } from "lucide-react";

export const columns: ColumnDef<any>[] = [
    { 
        accessorKey: "itemCode", 
        header: "Item Code" 
    },
    { 
        accessorKey: "itemName", 
        header: "Item Name" 
    },
    { 
        accessorKey: "category", 
        header: "Category" 
    },
    { 
        accessorKey: "price", 
        header: () => <div className="text-right">Price</div>,
        cell: ({ row }) => {
            const amount = parseFloat(row.getValue("price"))
            const formatted = new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: "USD",
            }).format(amount)

            return <div className="text-right font-medium">{formatted}</div>
        },
    },
    { 
        accessorKey: "createdAt", 
        header: "Created At" 
    },
    { 
        accessorKey: "actions", 
        cell: ({ row }) => {
            const item = row.original
       
            return (
                <div className="flex flex-row gap-4">
                    <button className="bg-transparent cursor-pointer"><Pencil className="text-yellow-400 size-4" /></button>
                    <button className="bg-transparent cursor-pointer"><Trash2 className="text-red-400 size-4" /></button>
                </div>
            )
          },
      
    },
]