'use server'

import { auth } from "@/auth"
import prisma from "@/lib/prisma"

export async function getOrders() {
  try {
    const session = await auth()
    if (!session?.user) {
      throw new Error("Unauthorized")
    }

    const orders = await prisma.order.findMany({
      include: {
        shop: true,
        stock: true
      },
      orderBy: {
        order_date: 'desc'
      }
    })

    return { success: true, data: orders }
  } catch (error) {
    console.error("Error fetching orders:", error)
    return { success: false, error: "Failed to fetch orders" }
  }
}

export async function getOrderById(id: number) {
  try {
    const session = await auth()
    if (!session?.user) {
      throw new Error("Unauthorized")
    }

    const order = await prisma.order.findUnique({
      where: {
        order_id: id
      },
      include: {
        shop: true,
        stock: true
      }
    })

    if (!order) {
      return { success: false, error: "Order not found" }
    }

    return { success: true, data: order }
  } catch (error) {
    console.error("Error fetching order:", error)
    return { success: false, error: "Failed to fetch order" }
  }
} 