"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// Sample delivery data
const deliveries = [
  {
    deliveryId: "DEL001",
    orderId: "ORD001",
    customerName: "<PERSON>",
    address: "123 Main St, New York, NY 10001",
    date: "2025-05-29",
  },
  {
    deliveryId: "DEL002",
    orderId: "ORD002",
    customerName: "Sarah Johnson",
    address: "456 Oak Ave, Los Angeles, CA 90210",
    date: "2025-05-29",
  },
  {
    deliveryId: "DEL003",
    orderId: "ORD003",
    customerName: "<PERSON>",
    address: "789 Pine Rd, Chicago, IL 60601",
    date: "2025-05-28",
  },
  {
    deliveryId: "DEL004",
    orderId: "ORD004",
    customerName: "Emily Davis",
    address: "321 Elm St, Houston, TX 77001",
    date: "2025-05-28",
  },
  {
    deliveryId: "DEL005",
    orderId: "ORD005",
    customerName: "<PERSON>",
    address: "654 Maple Dr, Phoenix, AZ 85001",
    date: "2025-05-27",
  },
  {
    deliveryId: "DEL006",
    orderId: "ORD006",
    customerName: "Lisa Anderson",
    address: "987 Cedar Ln, Philadelphia, PA 19101",
    date: "2025-05-27",
  },
  {
    deliveryId: "DEL007",
    orderId: "ORD007",
    customerName: "Robert Taylor",
    address: "147 Birch Way, San Antonio, TX 78201",
    date: "2025-05-26",
  },
  {
    deliveryId: "DEL008",
    orderId: "ORD008",
    customerName: "Jennifer Martinez",
    address: "258 Spruce St, San Diego, CA 92101",
    date: "2025-05-26",
  },
  {
    deliveryId: "DEL009",
    orderId: "ORD009",
    customerName: "Christopher Lee",
    address: "369 Willow Ave, Dallas, TX 75201",
    date: "2025-05-25",
  },
  {
    deliveryId: "DEL010",
    orderId: "ORD010",
    customerName: "Amanda White",
    address: "741 Poplar Rd, San Jose, CA 95101",
    date: "2025-05-25",
  },
]

export default function RecentDeliveries() {
  return (
    <div className="w-full  rounded-lg overflow-hidden">
      <h2 className="text-2xl font-bold mb-6 px-6 pt-6">Recent Deliveries</h2>
      <Table>
        <TableHeader>
          <TableRow >
            <TableHead >Delivery ID</TableHead>
            <TableHead >Order ID</TableHead>
            <TableHead >Customer Name</TableHead>
            <TableHead >Address</TableHead>
            <TableHead >Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {deliveries.map((delivery) => (
            <TableRow
              key={delivery.deliveryId}
              
            >
              <TableCell >{delivery.deliveryId}</TableCell>
              <TableCell >{delivery.orderId}</TableCell>
              <TableCell >{delivery.customerName}</TableCell>
              <TableCell >{delivery.address}</TableCell>
              <TableCell >{delivery.date}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
