import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      productName,
      category,
      quantity,
      unitPrice,
      dateAdded,
      isMainStock,
    } = body;

    // Find category_id by category name
    const categoryRecord = await prisma.category.findFirst({
      where: { category_name: category },
    });

    if (!categoryRecord) {
      return NextResponse.json({ error: 'Category not found' }, { status: 400 });
    }

    const stock = await prisma.stock.create({
      data: {
        item_name: productName,
        category_id: categoryRecord.category_id,
        quantity: Number(quantity),
        price: Number(unitPrice),
        is_main_stock: isMainStock === 'Main Stock',
        date_added: new Date(dateAdded),
      },
    });

    return NextResponse.json(stock, { status: 201 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to add stock item', details: String(error) }, { status: 500 });
  }
}