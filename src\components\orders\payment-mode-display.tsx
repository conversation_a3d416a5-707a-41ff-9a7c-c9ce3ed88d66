import { DollarSign, CreditCard, Banknote, FileText } from "lucide-react"

interface PaymentModeDisplayProps {
  payment: "cash" | "credit" | "check" | "mixed"
}

export function PaymentModeDisplay({ payment }: PaymentModeDisplayProps) {
  const getPaymentIcon = () => {
    switch (payment) {
      case "cash":
        return <Banknote className="h-4 w-4" />
      case "credit":
        return <CreditCard className="h-4 w-4" />
      case "check":
        return <FileText className="h-4 w-4" />
      case "mixed":
        return <DollarSign className="h-4 w-4" />
      default:
        return null
    }
  }

  return (
    <div className="flex items-center gap-1">
      {getPaymentIcon()}
      <span className="capitalize">{payment}</span>
    </div>
  )
}
