import { Trash2 } from "lucide-react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "../ui/alert-dialog";

interface DeleteCategoryProps {
    categoryId: number;
    categoryName: string;
    onDelete: (categoryId: number) => void;
}

export default function DeleteCategory({ categoryId, categoryName, onDelete }: DeleteCategoryProps) {
    return (
        <>
            <AlertDialog>
                <AlertDialogTrigger asChild>
                    <button className="bg-transparent cursor-pointer"><Trash2 className="text-red-400 size-4" /></button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone.<br />
                            This will permanently delete " {categoryName} " from your list.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction className="bg-red-500" onClick={() => onDelete(categoryId)}>Delete</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    )
}