"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Archive,
  FileText,
  Home,
  Menu,
  Package,
  ShoppingBag,
  ShoppingCart,
  X,
  Truck,
  Layers2
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,

} from "@/components/ui/sidebar"

const items = [
  { title: "Overview", url: "/app", icon: Home },
  { title: "Item", url: "/app/items", icon: Package },
  { title: "Stock", url: "/app/stock", icon: Archive },
  { title: "Orders", url: "/app/orders", icon: ShoppingBag },
  { title: "Shops", url: "/app/shops", icon: ShoppingCart },
  { title: "Invoices", url: "/app/invoices", icon: FileText },
  { title: "Delivery", url: "/app/delivery", icon: Truck },
  { title: "Category", url: "/app/category", icon: Layers2 },
]

interface AppSidebarProps{
  setHeaderName:any
}

export function AppSidebar({setHeaderName}: AppSidebarProps) {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  const toggleSidebar = () => setIsOpen(!isOpen)

  const SidebarItems = () => (
    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel className="text-lg h-16 px-4">Dashboard</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            {items.map((item) => {
              const isActive = pathname === item.url
              
              return (
                <SidebarMenuItem
                  key={item.title}
                  className={`h-12 rounded-lg ${
                    isActive
                      ? "border-2 border-gray-200 font-semibold text-primary"
                      : ""
                  }`}
                  onClick={()=>setHeaderName(item.title + (item.title !== "Overview" ? " Management": " "))}
                >
                  <SidebarMenuButton asChild className="py-6">
                    <Link
                      href={item.url}
                      className="flex items-center gap-2 px-4 w-full"
                      onClick={() => setIsOpen(false)} // Close on mobile click
                    >
                      <item.icon className="w-5 h-5" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              )
            })}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>
  )

  return (
    <>
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button onClick={toggleSidebar} className="p-2 rounded-md bg-gray-200">
          {isOpen ? <X /> : <Menu />}
        </button>
      </div>

      <div className="hidden lg:block">
        <Sidebar className="w-64 min-h-screen border-r ">
          <SidebarItems />
        </Sidebar>
      </div>

      {isOpen && (
        <div className="lg:hidden fixed inset-0 z-40 bg-black/50" onClick={toggleSidebar}></div>
      )}

      <div
        className={`fixed top-0 left-0 h-full w-64 z-50  transform transition-transform duration-300 ${
          isOpen ? "translate-x-0" : "-translate-x-full"
        } lg:hidden`}
      >
        <Sidebar className="h-full border-r">
          <SidebarItems />
        </Sidebar>
      </div>
    </>
  )
}
