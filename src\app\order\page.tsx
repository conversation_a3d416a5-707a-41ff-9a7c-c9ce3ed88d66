"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Package,
  ShoppingCart,
  Store,
  FileText,
  TrendingUp,
  AlertTriangle,
  Plus,
  Edit,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  CreditCard,
  Banknote,
} from "lucide-react"

export default function RuwanDistributionDashboard() {
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [paymentMode, setPaymentMode] = useState("cash")

  // Sample data based on SRS requirements
  const stockCategories = [
    { id: 1, name: "Beverages", items: 15, stock: 850 },
    { id: 2, name: "Snacks", items: 18, stock: 1200 },
    { id: 3, name: "Dairy Products", items: 12, stock: 650 },
    { id: 4, name: "Household Items", items: 15, stock: 980 },
  ]

  const recentOrders = [
    { id: "ORD-001", shop: "Saman Store", items: 25, total: 15750, status: "delivered", payment: "cash" },
    { id: "ORD-002", shop: "Nimal Mart", items: 18, total: 12300, status: "pending", payment: "credit" },
    { id: "ORD-003", shop: "Kamal Shop", items: 32, total: 18900, status: "cancelled", payment: "check" },
    { id: "ORD-004", shop: "Sunil Store", items: 22, total: 14200, status: "delivered", payment: "mixed" },
  ]

  const registeredShops = [
    { code: "RT01-001", name: "Saman Store", contact: "0771234567", route: "Route 01", area: "Colombo" },
    { code: "RT02-002", name: "Nimal Mart", contact: "0779876543", route: "Route 02", area: "Kandy" },
    { code: "RT01-003", name: "Kamal Shop", contact: "0765432109", route: "Route 01", area: "Colombo" },
    { code: "RT03-004", name: "Sunil Store", contact: "0712345678", route: "Route 03", area: "Galle" },
  ]

  const lowStockItems = [
    { name: "Coca Cola 500ml", category: "Beverages", stock: 25, minStock: 50 },
    { name: "Milk Powder 400g", category: "Dairy Products", stock: 15, minStock: 30 },
    { name: "Bread", category: "Household Items", stock: 8, minStock: 20 },
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Ruwan Distribution Dashboard</h1>
            <p className="text-gray-600">Manage your distribution operations efficiently</p>
          </div>
          <div className="flex gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Stock
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Stock</DialogTitle>
                  <DialogDescription>Add new items to your inventory with invoice number</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="invoice">Invoice Number</Label>
                    <Input id="invoice" placeholder="INV-2025-001" />
                  </div>
                  <div>
                    <Label htmlFor="category">Category</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {stockCategories.map((cat) => (
                          <SelectItem key={cat.id} value={cat.name.toLowerCase()}>
                            {cat.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="item">Item Name</Label>
                    <Input id="item" placeholder="Enter item name" />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="quantity">Quantity</Label>
                      <Input id="quantity" type="number" placeholder="100" />
                    </div>
                    <div>
                      <Label htmlFor="price">Unit Price</Label>
                      <Input id="price" type="number" placeholder="150.00" />
                    </div>
                  </div>
                  <Button className="w-full">Add to Stock</Button>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Store className="w-4 h-4 mr-2" />
                  Register Shop
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Register New Shop</DialogTitle>
                  <DialogDescription>Add a new shop to your distribution network</DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="shopName">Shop Name</Label>
                    <Input id="shopName" placeholder="Enter shop name" />
                  </div>
                  <div>
                    <Label htmlFor="contactPerson">Contact Person</Label>
                    <Input id="contactPerson" placeholder="Enter contact person" />
                  </div>
                  <div>
                    <Label htmlFor="contactNumber">Contact Number</Label>
                    <Input id="contactNumber" placeholder="0771234567" />
                  </div>
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" placeholder="<EMAIL>" />
                  </div>
                  <div>
                    <Label htmlFor="shopCategory">Shop Category</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="supermarket">Supermarket</SelectItem>
                        <SelectItem value="grocery">Grocery Store</SelectItem>
                        <SelectItem value="convenience">Convenience Store</SelectItem>
                        <SelectItem value="wholesale">Wholesale</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="route">Service Route</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select route" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="route01">Route 01 - Colombo</SelectItem>
                        <SelectItem value="route02">Route 02 - Kandy</SelectItem>
                        <SelectItem value="route03">Route 03 - Galle</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor="otherNumber">Other General Number</Label>
                    <Input id="otherNumber" placeholder="Additional contact number" />
                  </div>
                </div>
                <Button className="w-full mt-4">Register Shop</Button>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Stock Value</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Rs. 2,45,750</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+12%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">23</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-orange-600">+3</span> new today
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Registered Shops</CardTitle>
              <Store className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">156</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+8</span> this week
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Rs. 8,95,420</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+18%</span> from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="stock">Stock Management</TabsTrigger>
            <TabsTrigger value="orders">Orders</TabsTrigger>
            <TabsTrigger value="shops">Shops</TabsTrigger>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Stock Categories */}
              <Card>
                <CardHeader>
                  <CardTitle>Stock Categories</CardTitle>
                  <CardDescription>Current inventory by category</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {stockCategories.map((category) => (
                    <div key={category.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{category.name}</p>
                        <p className="text-sm text-muted-foreground">{category.items} items</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{category.stock}</p>
                        <p className="text-sm text-muted-foreground">units</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Low Stock Alert */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-orange-500" />
                    Low Stock Alert
                  </CardTitle>
                  <CardDescription>Items running low on inventory</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {lowStockItems.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-muted-foreground">{item.category}</p>
                        </div>
                        <Badge variant="destructive">{item.stock} left</Badge>
                      </div>
                      <Progress value={(item.stock / item.minStock) * 100} className="h-2" />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Recent Orders */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Orders</CardTitle>
                <CardDescription>Latest order activities</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order ID</TableHead>
                      <TableHead>Shop</TableHead>
                      <TableHead>Items</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Payment</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recentOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.id}</TableCell>
                        <TableCell>{order.shop}</TableCell>
                        <TableCell>{order.items}</TableCell>
                        <TableCell>Rs. {order.total.toLocaleString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            {order.payment === "cash" && <Banknote className="h-4 w-4" />}
                            {order.payment === "credit" && <CreditCard className="h-4 w-4" />}
                            {order.payment === "check" && <FileText className="h-4 w-4" />}
                            {order.payment === "mixed" && <DollarSign className="h-4 w-4" />}
                            <span className="capitalize">{order.payment}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              order.status === "delivered"
                                ? "default"
                                : order.status === "pending"
                                  ? "secondary"
                                  : "destructive"
                            }
                          >
                            {order.status === "delivered" && <CheckCircle className="h-3 w-3 mr-1" />}
                            {order.status === "pending" && <Clock className="h-3 w-3 mr-1" />}
                            {order.status === "cancelled" && <XCircle className="h-3 w-3 mr-1" />}
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stock" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Stock Management</CardTitle>
                <CardDescription>Manage your inventory, add new stock, and handle restocking</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4 mb-6">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filter by category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {stockCategories.map((cat) => (
                        <SelectItem key={cat.id} value={cat.name.toLowerCase()}>
                          {cat.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Add New Item
                  </Button>
                  <Button variant="outline">
                    <Package className="w-4 h-4 mr-2" />
                    Restock Items
                  </Button>
                </div>

                <div className="text-center py-8 text-muted-foreground">
                  <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Stock management interface would be implemented here</p>
                  <p className="text-sm">Features: Add stock, resell, restock, category management</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="orders" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Order Management</CardTitle>
                <CardDescription>Handle deliveries, cancellations, and order editing</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex gap-4">
                    <Button>Process Delivery</Button>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline">Cancel Order</Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Cancel Order</DialogTitle>
                          <DialogDescription>Please provide a reason for cancellation</DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label>Reason for Cancellation</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select reason" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="out-of-stock">Out of Stock</SelectItem>
                                <SelectItem value="customer-request">Customer Request</SelectItem>
                                <SelectItem value="delivery-issue">Delivery Issue</SelectItem>
                                <SelectItem value="payment-issue">Payment Issue</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="comment">Additional Comments</Label>
                            <Textarea id="comment" placeholder="Enter additional details..." />
                          </div>
                          <Button className="w-full" variant="destructive">
                            Cancel Order
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button variant="outline">Edit Order</Button>
                  </div>

                  <div className="text-center py-8 text-muted-foreground">
                    <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Order management interface would be implemented here</p>
                    <p className="text-sm">Features: Delivery processing, order editing, cancellation with reasons</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="shops" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Shop Management</CardTitle>
                <CardDescription>Manage registered shops, routes, and shop codes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex gap-4">
                    <Button>Bulk Route Change</Button>
                    <Button variant="outline">Update Shop Codes</Button>
                    <Button variant="outline">Add Shop Category</Button>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Shop Code</TableHead>
                        <TableHead>Shop Name</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Route</TableHead>
                        <TableHead>Service Area</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {registeredShops.map((shop) => (
                        <TableRow key={shop.code}>
                          <TableCell className="font-medium">{shop.code}</TableCell>
                          <TableCell>{shop.name}</TableCell>
                          <TableCell>{shop.contact}</TableCell>
                          <TableCell>{shop.route}</TableCell>
                          <TableCell>{shop.area}</TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button size="sm" variant="outline">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="outline">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="invoices" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Invoice Management</CardTitle>
                <CardDescription>Handle invoicing with multiple payment modes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">Payment Modes</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Banknote className="h-4 w-4" />
                          <span className="text-sm">Cash Payments</span>
                          <Badge variant="secondary">45%</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <CreditCard className="h-4 w-4" />
                          <span className="text-sm">Credit</span>
                          <Badge variant="secondary">30%</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span className="text-sm">Check</span>
                          <Badge variant="secondary">15%</Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4" />
                          <span className="text-sm">Mixed</span>
                          <Badge variant="secondary">10%</Badge>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">Quick Invoice</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select shop" />
                          </SelectTrigger>
                          <SelectContent>
                            {registeredShops.map((shop) => (
                              <SelectItem key={shop.code} value={shop.code}>
                                {shop.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Select value={paymentMode} onValueChange={setPaymentMode}>
                          <SelectTrigger>
                            <SelectValue placeholder="Payment mode" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="cash">Cash</SelectItem>
                            <SelectItem value="credit">Credit</SelectItem>
                            <SelectItem value="check">Check</SelectItem>
                            <SelectItem value="mixed">Mixed Payment</SelectItem>
                          </SelectContent>
                        </Select>
                        <Button className="w-full">Create Invoice</Button>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">Invoice Stats</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Today</span>
                          <span className="font-medium">23</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">This Week</span>
                          <span className="font-medium">156</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">This Month</span>
                          <span className="font-medium">642</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Total Value</span>
                          <span className="font-medium">Rs. 8,95,420</span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Invoice management interface would be implemented here</p>
                    <p className="text-sm">Features: Invoice generation, payment mode handling, invoice formatting</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
