import { ColumnDef } from "@tanstack/react-table";
import DeleteShop from "./delete-shop";
import UpdateShop from "./update-shop";

interface Shop {
    shop_id: number;
    shop_code: string;
    name: string;
    service_area: string;
    registration_number: string;
    contact_number?: string;
    email?: string;
    route_id: number;
    category_id: number;
    category: {
        category_id: number;
        category_name: string;
    };
    route: {
        route_id: number;
        route_code: string;
        description: string;
    };
    _count: {
        orders: number;
    };
}

export const createShopColumns = (onShopUpdated?: () => void): ColumnDef<Shop>[] => [
    {
        accessorKey: "shop_id",
        header: "Shop ID"
    },
    {
        accessorKey: "shop_code",
        header: "Shop Code"
    },
    {
        accessorKey: "name",
        header: "Shop Name"
    },
    {
        accessorKey: "registration_number",
        header: "Registration Number"
    },
    {
        accessorKey: "service_area",
        header: "Service Area"
    },
    {
        accessorKey: "contact_number",
        header: "Contact"
    },
    {
        accessorKey: "email",
        header: "Email"
    },
    {
        accessorFn: (row) => row.category.category_name,
        header: "Category"
    },
    {
        accessorFn: (row) => row.route.description,
        header: "Route"
    },
    {
        accessorFn: (row) => row._count.orders,
        header: "Total Orders"
    },
    {
        id: "actions",
        header: "Actions",
        cell: ({ row }) => {
            const shop = row.original;

            return (
                <div className="flex flex-row gap-4">
                    <UpdateShop shop={shop} onShopUpdated={onShopUpdated} />
                    <DeleteShop shopId={shop.shop_id} name={shop.name} onShopDeleted={onShopUpdated} />
                </div>
            )
          },
    },
];

// For backward compatibility
export const shopColumns = createShopColumns();