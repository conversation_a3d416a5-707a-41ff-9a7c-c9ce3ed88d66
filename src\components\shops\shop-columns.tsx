import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, Trash2, Pencil } from "lucide-react";
import DeleteShop from "./delete-shop";
import UpdateShop from "./update-shop";

export const shopColumns: ColumnDef<any>[] = [
    { 
        accessorKey: "id", 
        header: "Shop Id" 
    },
    { 
        accessorKey: "shopCode", 
        header: "Shop Code" 
    },
    { 
        accessorKey: "regNumber", 
        header: "Reg_Number" 
    },
    { 
        accessorKey: "shopName", 
        header: "Shop Name" 
    },
    { 
        accessorKey: "category", 
        header: "Category" 
    },
    { 
        accessorKey: "contact", 
        header: "Contact" 
    },
    { 
        accessorKey: "email", 
        header: "Email" 
    },
    { 
        accessorKey: "createdAt", 
        header: "Created At" 
    },
    { 
        accessorKey: "actions", 
        cell: ({ row }) => {
            const shop = row.original
       
            return (
                <div className="flex flex-row gap-4">
                    <UpdateShop shopName={shop.shopName} regNo={shop.regNumber} category={shop.category} contact={shop.contact} email={shop.email} />
                    <DeleteShop id={shop.id} name={shop.shopName} />
                </div>
            )
          },
      
    },
]