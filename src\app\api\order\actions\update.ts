'use server'

import { auth } from "@/auth"
import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"

export async function updateOrder(id: number, data: {
  status?: string
  reason?: string
  quantity?: number
}) {
  try {
    const session = await auth()
    if (!session?.user) {
      throw new Error("Unauthorized")
    }

    const updatedOrder = await prisma.order.update({
      where: {
        order_id: id
      },
      data,
      include: {
        shop: true,
        stock: true
      }
    })

    revalidatePath('/orders')
    return { success: true, data: updatedOrder }
  } catch (error) {
    console.error("Error updating order:", error)
    return { success: false, error: "Failed to update order" }
  }
} 