import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";
import { prisma } from "@/prisma";

export const { handlers, auth } = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Userna<PERSON>", type: "text", placeholder: "johndoe" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const { username, password } = credentials as {
          username: string;
          password: string;
        };

        console.log("credentials", credentials);
        console.log("username", username);
        console.log("password", password);

        const user = await prisma.user.findUnique({
          where: { username },
        });

        console.log("user", user);

        if (!user) return null;

        // Plain text password comparison (not recommended for production)
        if (password !== user.password) return null;

        return user;
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (token.sub) {
        session.user.id = token.sub;
      }
      return session;
    },
  },
  pages: {
    signIn: "/sign-in",
    newUser: "/sign-up",
    signOut: "/sign-out",
  },
});
