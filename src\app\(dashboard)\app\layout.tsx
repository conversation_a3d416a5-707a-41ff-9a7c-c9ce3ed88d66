import "../../globals.css";
import type React from "react";
import {auth} from "@/auth";
import NotAuthenticated from "@/components/not-authenticated";
import { SidebarProvider } from "@/components/ui/sidebar";
import Dashboard from "./Dashboard";



export const metadata = {
    title: "SaaSDevSuite user App",
    description: "A modern, powerful and user-friendly dashboard for your SaaS/MicrosSaaS project.",
};

export default async function RootLayout({
                                             children,
                                         }: {
    children: React.ReactNode;
}) {
    const session = await auth();
    if (!session) return <NotAuthenticated/>;


    return (
    <SidebarProvider className="w-full">
        <Dashboard children={children}/>
    </SidebarProvider>
    );
}
