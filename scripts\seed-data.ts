import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedData() {
  try {
    // Create categories
    const categories = [
      { category_name: 'Supermarket' },
      { category_name: 'Grocery Store' },
      { category_name: 'Convenience Store' },
      { category_name: 'Wholesale' },
      { category_name: 'Pharmacy' },
      { category_name: 'Restaurant' }
    ];

    console.log('Creating categories...');
    for (const category of categories) {
      const existing = await prisma.category.findFirst({
        where: { category_name: category.category_name }
      });
      if (!existing) {
        await prisma.category.create({
          data: category
        });
      }
    }

    // Create routes
    const routes = [
      { route_code: 'RT01', description: 'Colombo Central' },
      { route_code: 'RT02', description: 'Kandy District' },
      { route_code: 'RT03', description: 'Galle Southern' },
      { route_code: 'RT04', description: 'Negombo West' },
      { route_code: 'RT05', description: 'Kurunegala North' }
    ];

    console.log('Creating routes...');
    for (const route of routes) {
      const existing = await prisma.route.findFirst({
        where: { route_code: route.route_code }
      });
      if (!existing) {
        await prisma.route.create({
          data: route
        });
      }
    }

    console.log('Seed data created successfully!');
  } catch (error) {
    console.error('Error seeding data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedData();
