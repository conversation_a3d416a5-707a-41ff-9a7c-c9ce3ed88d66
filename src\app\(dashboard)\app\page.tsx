"use client";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Tooltip } from "@/components/ui/tooltip";
import { useSession } from "next-auth/react";
import { useState } from "react";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

const Page = () => {
    const session = useSession();
    const sampleOrders = [
        {
          id: "ORD001",
          customer: "<PERSON> Do<PERSON>",
          amount: "$150.00",
          status: "Processing",
          order_date: "2023-10-27",
          shop_id: "SHOP001",
          items: [
            { stock_id: "STK001", item_name: "Item A", quantity: 2, price: "$50.00" },
            { stock_id: "STK002", item_name: "Item B", quantity: 1, price: "$100.00" },
          ],
        },
        {
          id: "ORD002",
          customer: "<PERSON>",
          amount: "$230.50",
          status: "Shipped",
          order_date: "2023-10-26",
          shop_id: "SHOP002",
          items: [
            { stock_id: "STK003", item_name: "Item C", quantity: 5, price: "$40.00" },
            { stock_id: "STK004", item_name: "Item D", quantity: 3, price: "$10.17" },
          ],
        },
        {
          id: "ORD003",
          customer: "Peter Jones",
          amount: "$75.20",
          status: "Delivered",
          order_date: "2023-10-25",
          shop_id: "SHOP001",
          items: [
            { stock_id: "STK005", item_name: "Item E", quantity: 1, price: "$75.20" },
          ],
        },
      ];
    
      // Calculate sample statistics
      const totalOrders = sampleOrders.length;
      const processingOrders = sampleOrders.filter(order => order.status === "Processing").length;
      const shippedOrders = sampleOrders.filter(order => order.status === "Shipped").length;
      const deliveredOrders = sampleOrders.filter(order => order.status === "Delivered").length;
      const cancelledOrders = sampleOrders.filter(order => order.status === "Cancelled").length;
      const totalRevenue = sampleOrders.reduce((sum, order) => sum + parseFloat(order.amount.replace("$", "")), 0).toFixed(2);
    
      // Data for Status Distribution Pie Chart
      const statusData = [
        { name: 'Processing', value: processingOrders },
        { name: 'Shipped', value: shippedOrders },
        { name: 'Delivered', value: deliveredOrders },
        { name: 'Cancelled', value: cancelledOrders },
      ];
    
      const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']; // Sample colors
    
      // Add state for delivery dialog
      const [showDeliveryDialog, setShowDeliveryDialog] = useState(false);
      const [selectedOrderForDelivery, setSelectedOrderForDelivery] = useState(null);
    return (

        <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="col-span-1">
                <div className="p-4 rounded-lg border border-gray-100/20">
                    <div className="flex items-center justify-between">
                        <h3>Total Stock Value</h3>
                        <svg
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-6" >
                            <path strokeLinecap="round" strokeLinejoin="round" d="m21 7.5-9-5.25L3 7.5m18 0-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9" />
                        </svg>
                    </div>
                    <p className="text-2xl font-bold">RS. 100,000</p>
                    <p className="text-sm "><span className="text-green-500">+12%</span> from last month</p>
                </div>
            </div>
            <div className="col-span-1">
                <div className="p-4 rounded-lg border border-gray-100/20">
                    <div className="flex items-center justify-between">
                        <h3>Pending Orders</h3>
                        <svg
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-6">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.272M6 20.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm12.75 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z" />
                        </svg>
                    </div>
                    <p className="text-2xl font-bold">23</p>
                    <p className="text-sm "><span className="text-green-500">+3</span> new today</p>
                </div>
            </div>
            <div className="col-span-1">
                <div className="p-4 rounded-lg border border-gray-100/20">
                    <div className="flex items-center justify-between">
                        <h3>Registered Shops</h3>
                        <svg
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-6">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z" />
                        </svg>
                    </div>
                    <p className="text-2xl font-bold">156</p>
                    <p className="text-sm "><span className="text-green-500">+8</span> from last month</p>
                </div>
            </div>
            <div className="col-span-1">
                <div className="p-4 rounded-lg border border-gray-100/20">
                    <div className="flex items-center justify-between">
                        <h3>Monthly Revenue</h3>
                        <svg
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="size-6">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941" />
                        </svg>
                    </div>
                    <p className="text-2xl font-bold">RS. 8,432,500</p>
                    <p className="text-sm "><span className="text-green-500">+18%</span> from last month</p>
                </div>
            </div>
            <div>
                
            </div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mt-4">
        {/* Total Orders Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalOrders}</div>
          </CardContent>
        </Card>
        {/* Total Revenue Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRevenue}</div>
          </CardContent>
        </Card>
        {/* Orders Shipped Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders Shipped</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{shippedOrders}</div>
          </CardContent>
        </Card>
        {/* Orders Cancelled Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders Cancelled</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{cancelledOrders}</div>
          </CardContent>
        </Card>
      </div>

      {/* Order Status Distribution Chart Card - Full Width */}
      <div className="grid gap-4 mt-4">{/* New grid for full width chart */}
        <Card className="col-span-full">{/* Card spans full width */}
          <CardHeader>
            <CardTitle className="text-lg font-medium">Order Status Distribution</CardTitle>{/* Made title larger */}
          </CardHeader>
          <CardContent className="flex items-center justify-center">
            <ResponsiveContainer width="100%" height={300}> {/* Increased height */}
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
        </>
    );
};

export default Page;