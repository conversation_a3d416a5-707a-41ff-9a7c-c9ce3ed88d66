import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function PUT(req: NextRequest) {
  try {
    const { stock_id, quantity, price, date_added, is_main_stock } = await req.json();

    if (!stock_id) {
      return NextResponse.json({ error: 'stock_id is required' }, { status: 400 });
    }

    const updated = await prisma.stock.update({
      where: { stock_id: Number(stock_id) },
      data: {
        quantity: quantity !== undefined ? Number(quantity) : undefined,
        price: price !== undefined ? Number(price) : undefined,
        date_added: date_added ? new Date(date_added) : undefined,
        is_main_stock: typeof is_main_stock === 'boolean' ? is_main_stock : undefined,
      },
    });

    return NextResponse.json({ success: true, updated }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update stock item', details: String(error) }, { status: 500 });
  }
}