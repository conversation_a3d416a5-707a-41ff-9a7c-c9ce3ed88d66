import { Trash2 } from "lucide-react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "../ui/alert-dialog";
import { useState } from "react";
import { deleteShop } from "@/app/api/shops/actions/delete";
import { toast } from "sonner";

interface DeleteShopProps {
    shopId: number;
    name: string;
    onShopDeleted?: () => void;
}

export default function DeleteShop({ shopId, name, onShopDeleted }: DeleteShopProps) {
    const [loading, setLoading] = useState(false);

    const handleDelete = async () => {
        try {
            setLoading(true);
            const result = await deleteShop(shopId);

            if (result.success) {
                toast.success("Shop deleted successfully!");
                onShopDeleted?.();
            } else {
                toast.error(result.error || "Failed to delete shop");
            }
        } catch (error) {
            toast.error("Error deleting shop");
            console.error("Error deleting shop:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                <button className="bg-transparent cursor-pointer"><Trash2 className="text-red-400 size-4" /></button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete "{name}" from your list.
                        {loading && " Please wait..."}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-red-500 text-white"
                        onClick={handleDelete}
                        disabled={loading}
                    >
                        {loading ? "Deleting..." : "Delete"}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}