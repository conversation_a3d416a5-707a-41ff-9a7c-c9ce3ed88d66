import { Plus } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON>Header, <PERSON>alogTitle, DialogTrigger } from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "../ui/select";
import { useState, useEffect } from "react";
import { createShop } from "@/app/api/shops/actions/create";
import { toast } from "sonner";

interface Category {
    categoryId: number;
    categoryName: string;
}

interface Route {
    routeId: number;
    routeCode: string;
    description: string;
}

interface AddShopProps {
    onShopAdded?: () => void;
}

export default function AddShop({ onShopAdded }: AddShopProps) {
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [categories, setCategories] = useState<Category[]>([]);
    const [routes, setRoutes] = useState<Route[]>([]);

    const [formData, setFormData] = useState({
        name: "",
        registration_number: "",
        category_id: "",
        route_id: "",
        service_area: ""
    });

    useEffect(() => {
        if (open) {
            fetchCategories();
            fetchRoutes();
        }
    }, [open]);

    const fetchCategories = async () => {
        try {
            const response = await fetch("/api/categories/read");
            if (response.ok) {
                const data = await response.json();
                setCategories(data);
            }
        } catch (error) {
            console.error("Error fetching categories:", error);
        }
    };

    const fetchRoutes = async () => {
        try {
            const response = await fetch("/api/routes/read");
            if (response.ok) {
                const data = await response.json();
                setRoutes(data);
            }
        } catch (error) {
            console.error("Error fetching routes:", error);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.name || !formData.registration_number || !formData.category_id ||
            !formData.route_id || !formData.service_area) {
            toast.error("Please fill in all fields");
            return;
        }

        try {
            setLoading(true);
            const result = await createShop({
                name: formData.name,
                registration_number: formData.registration_number,
                category_id: parseInt(formData.category_id),
                route_id: parseInt(formData.route_id),
                service_area: formData.service_area
            });

            if (result.success) {
                toast.success("Shop created successfully!");
                setFormData({
                    name: "",
                    registration_number: "",
                    category_id: "",
                    route_id: "",
                    service_area: ""
                });
                setOpen(false);
                onShopAdded?.();
            } else {
                toast.error(typeof result.error === 'string' ? result.error : "Failed to create shop");
            }
        } catch (error) {
            toast.error("Error creating shop");
            console.error("Error creating shop:", error);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (field: string, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild className="cursor-pointer">
                <Button><Plus /> Add Shop</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Add A New Shop</DialogTitle>
                    <DialogDescription>
                        Add a new shop to your list here.
                    </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="shopName" className="text-right"> Shop Name </Label>
                            <Input
                                id="shopName"
                                placeholder="Enter your Shop Name"
                                className="col-span-3"
                                value={formData.name}
                                onChange={(e) => handleInputChange("name", e.target.value)}
                                required
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="regNo" className="text-right"> Reg No </Label>
                            <Input
                                id="regNo"
                                placeholder="Enter Shop Registration Number"
                                className="col-span-3"
                                value={formData.registration_number}
                                onChange={(e) => handleInputChange("registration_number", e.target.value)}
                                required
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="serviceArea" className="text-right"> Service Area </Label>
                            <Input
                                id="serviceArea"
                                placeholder="Enter Service Area"
                                className="col-span-3"
                                value={formData.service_area}
                                onChange={(e) => handleInputChange("service_area", e.target.value)}
                                required
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="category" className="text-right"> Category </Label>
                            <Select value={formData.category_id} onValueChange={(value) => handleInputChange("category_id", value)}>
                                <SelectTrigger className="w-[280px]">
                                    <SelectValue placeholder="Select Category" />
                                </SelectTrigger>
                                <SelectContent>
                                    {categories.map((category) => (
                                        <SelectItem key={category.categoryId} value={category.categoryId.toString()}>
                                            {category.categoryName}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="route" className="text-right"> Route </Label>
                            <Select value={formData.route_id} onValueChange={(value) => handleInputChange("route_id", value)}>
                                <SelectTrigger className="w-[280px]">
                                    <SelectValue placeholder="Select Route" />
                                </SelectTrigger>
                                <SelectContent>
                                    {routes.map((route) => (
                                        <SelectItem key={route.routeId} value={route.routeId.toString()}>
                                            {route.routeCode} - {route.description}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                    </div>
                    <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => setFormData({
                            name: "",
                            registration_number: "",
                            category_id: "",
                            route_id: "",
                            service_area: ""
                        })}>
                            Clear Form
                        </Button>
                        <Button type="submit" disabled={loading}>
                            {loading ? "Adding..." : "Add Shop"}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}