import { Plus } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "../ui/select";

export default function AddShop() {
    return (
        <Dialog>
            <DialogTrigger asChild className="cursor-pointer">
                <Button><Plus /> Add Shop</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Add A New Shop</DialogTitle>
                    <DialogDescription>
                        Add a new shop to your list here.
                    </DialogDescription>
                </DialogHeader>
                <form>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="shopName" className="text-right"> Shop Name </Label>
                            <Input id="shopName" placeholder="Enter your Shop Name" className="col-span-3" />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="username" className="text-right"> Reg No </Label>
                            <Input id="username" placeholder="Enter Shop Registration Number" className="col-span-3" />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="category" className="text-right"> Category </Label>
                            <Select>
                                <SelectTrigger className="w-[280px]">
                                    <SelectValue placeholder="Select Category" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Food">Food</SelectItem>
                                    <SelectItem value="Tech">Tech</SelectItem>
                                    <SelectItem value="Clothing">Clothing</SelectItem>
                                    <SelectItem value="Books">Books</SelectItem>
                                    <SelectItem value="Home">Home</SelectItem>
                                    <SelectItem value="Crafts">Crafts</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="contact" className="text-right"> Contact </Label>
                            <Input id="contact" placeholder="Contact number" className="col-span-3" />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="email" className="text-right"> Email </Label>
                            <Input type="email" id="email" placeholder="Email address" className="col-span-3" />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button type="reset">Clear Form</Button>
                        <Button type="submit">Add Shop</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}