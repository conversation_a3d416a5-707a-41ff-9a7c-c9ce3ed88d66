'use server'

import { auth } from "@/auth"
import prisma from "@/lib/prisma"

export async function getShops(filters?: {
  route_id?: number
  category_id?: number
}) {
  try {
    const session = await auth()
    if (!session?.user) {
      throw new Error("Unauthorized")
    }

    const shops = await prisma.shop.findMany({
      where: filters,
      include: {
        category: true,
        route: true,
        _count: {
          select: {
            orders: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    })

    return { success: true, data: shops }
  } catch (error) {
    console.error("Error fetching shops:", error)
    return { success: false, error: "Failed to fetch shops" }
  }
}

export async function getShop(shopId: number) {
  try {
    const session = await auth()
    if (!session?.user) {
      throw new Error("Unauthorized")
    }

    const shop = await prisma.shop.findUnique({
      where: { shop_id: shopId },
      include: {
        category: true,
        route: true,
        orders: {
          include: {
            stock: true,
          },
          orderBy: {
            order_date: 'desc',
          },
        },
      },
    })

    if (!shop) {
      return { success: false, error: "Shop not found" }
    }

    return { success: true, data: shop }
  } catch (error) {
    console.error("Error fetching shop:", error)
    return { success: false, error: "Failed to fetch shop" }
  }
} 