import { Plus } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Dialog, <PERSON>alogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";

interface addCategoryProp{
    createCategory: (categoryName: string) => void;
}


export default function AddCategory({createCategory}: addCategoryProp) {
    
    function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        
        const categoryName = formData.get("categoryName") as string;
        
        createCategory(categoryName);
    }

    return (
        <Dialog>
            <DialogTrigger asChild className="cursor-pointer">
                <Button><Plus /> Add Category</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">

                <DialogHeader>
                    <DialogTitle>Add A New Category</DialogTitle>

                    <DialogDescription>
                        Add a new category to your list here.
                    </DialogDescription>

                </DialogHeader>
                <form onSubmit={handleSubmit}>
                <div className="grid gap-4 py-4">
                    <Label htmlFor="categoryName" className="text-right">Category Name :-</Label>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Input id="categoryName" name="categoryName" placeholder="Category Name" className="col-span-3" />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="reset">Clear Form</Button>
                    <DialogClose asChild>
                        <Button type="submit" >Add New Category</Button>
                    </DialogClose>
                </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}