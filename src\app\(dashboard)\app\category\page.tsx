"use client";

import AddCategory from "@/components/category/add-category";
import { CategoryColumns } from "@/components/category/category-columns";
import { ShopsTable } from "@/components/shops/shops-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface Category {
    categoryId: number;
    categoryName: string;
}

export default function CategoryPage(){
    const [searchTerm, setSearchTerm] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const categoriesPerPage = 16;
    const [categories, setCategories] = useState<Category[]>([]);

    useEffect(() => {
        const fetchCategories = async () => {
          try {
            const res = await fetch("/api/categories/read");
            if (!res.ok) {
              throw new Error(`HTTP error! status: ${res.status}`);
            }
            const data = await res.json();

            if (Array.isArray(data)) {
              setCategories(data);
            } else {
              console.error("API returned non-array data:", data);
              setCategories([]);
            }
          } catch (error) {
            toast.error("Error fetching categories:"+ error);
            setCategories([]);
          }
        };
        
        fetchCategories();

    }, []);

    const createCategory = async (categoryName: string) => {
      try {
        const res = await fetch("/api/categories/create", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ categoryName }),
        });
    
        const data = await res.json();
    
        if (!res.ok) {
          console.error("Create category failed:", data);
          throw new Error(data.error || "Failed to create category");
        }
    
        setCategories([...categories, data]);
        toast.success("Category created successfully");
        return data;
      } catch (err) {
        toast.error("Error in createCategory:"+ err);
      }
    };

    const updateCategory = async (categoryId: number, categoryName: string) => {
      try {
        const res = await fetch("/api/categories/update", {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({categoryId, categoryName}),
        });
    
        const data = await res.json();
    
        if (!res.ok) {
          throw new Error(data.error || "Failed to update category");
        }
    
        setCategories(categories.map(category => category.categoryId === categoryId ? { ...category, categoryName: categoryName } : category));
    
        toast.success("Category updated successfully");
        return data;
      } catch (err) {
        toast.error("Error in update category:"+ err);
      }
    };
    
    const deleteCategory = async (categoryId: number) => {
      try {
        const res = await fetch("/api/categories/delete", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ categoryId }),
        });
    
        if (!res.ok) {
          throw new Error("Failed to delete category");
        }

        setCategories(categories.filter((category) => category.categoryId !== categoryId));
        toast.success("Category deleted successfully");
      } catch (error) {
        toast.error("Error deleting category: " + error);
      }
    };

    const filteredCategories = (categories || []).filter(category =>
        category.categoryId.toString().includes(searchTerm) ||
        category.categoryName.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const totalPages = Math.ceil(filteredCategories.length / categoriesPerPage);

    const currentCategories = filteredCategories.slice((currentPage - 1) * categoriesPerPage, currentPage * categoriesPerPage);

    const handleChangePage = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
        }
    };
    
    return(
        <div className="space-y-4">
            <div className="flex flex-row gap-6 items-center justify-between">
                
                <div className="flex flex-row gap-6 items-center">
                    <AddCategory createCategory={createCategory}/>
                </div>
            </div>
            <Input
                type="text"
                placeholder="Search categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-1/3"
            />
            <div>
                <ShopsTable data={currentCategories} columns={CategoryColumns({onUpdate:updateCategory, onDelete:deleteCategory})}></ShopsTable>
                <div className="flex items-center justify-end space-x-2 py-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleChangePage(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        Previous
                    </Button>
                    <span>Page {currentPage} of {totalPages}</span>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleChangePage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Next
                    </Button>
                </div>
            </div>
        </div>
    )
}
