'use server'

import { auth } from "@/auth"
import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { z } from "zod"

const createShopSchema = z.object({
  name: z.string().min(1, 'Shop name is required'),
  category_id: z.number(),
  route_id: z.number(),
  service_area: z.string().min(1, 'Service area is required'),
  registration_number: z.string().min(1, 'Registration number is required'),
})

export async function createShop(data: z.infer<typeof createShopSchema>) {
  try {
    const session = await auth()
    if (!session?.user) {
      throw new Error("Unauthorized")
    }

    // Validate input data
    const validatedData = createShopSchema.parse(data)

    // Generate shop code based on route and service area
    const route = await prisma.route.findUnique({
      where: { route_id: validatedData.route_id },
    })

    if (!route) {
      throw new Error("Route not found")
    }

    const shopCode = `${route.route_code}-${validatedData.service_area.substring(0, 3).toUpperCase()}`

    const shop = await prisma.shop.create({
      data: {
        ...validatedData,
        shop_code: shopCode,
      },
      include: {
        category: true,
        route: true,
      },
    })

    revalidatePath('/shops')
    return { success: true, data: shop }
  } catch (error) {
    console.error("Error creating shop:", error)
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors }
    }
    return { success: false, error: "Failed to create shop" }
  }
} 