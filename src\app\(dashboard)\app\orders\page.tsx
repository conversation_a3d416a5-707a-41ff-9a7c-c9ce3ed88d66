"use client";

import { <PERSON><PERSON><PERSON> } from "next"
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose, DialogDescription } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Pie<PERSON><PERSON>, Pie, Cell, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useState } from "react";
import DeliveryForm from "@/components/delivery/delivery-management";


export default function OrdersPage() {
  const router = useRouter();
  
  // Sample order data (enhanced to include more fields for the modal)
  const sampleOrders = [
    {
      id: "ORD001",
      customer: "John Doe",
      amount: "$150.00",
      status: "Processing",
      order_date: "2023-10-27",
      shop_id: "SHOP001",
      items: [
        { stock_id: "STK001", item_name: "Item A", quantity: 2, price: "$50.00" },
        { stock_id: "STK002", item_name: "Item B", quantity: 1, price: "$100.00" },
      ],
    },
    {
      id: "ORD002",
      customer: "Jane Smith",
      amount: "$230.50",
      status: "Shipped",
      order_date: "2023-10-26",
      shop_id: "SHOP002",
      items: [
        { stock_id: "STK003", item_name: "Item C", quantity: 5, price: "$40.00" },
        { stock_id: "STK004", item_name: "Item D", quantity: 3, price: "$10.17" },
      ],
    },
    {
      id: "ORD003",
      customer: "Peter Jones",
      amount: "$75.20",
      status: "Delivered",
      order_date: "2023-10-25",
      shop_id: "SHOP001",
      items: [
        { stock_id: "STK005", item_name: "Item E", quantity: 1, price: "$75.20" },
      ],
    },
  ];

  // Calculate sample statistics
  const totalOrders = sampleOrders.length;
  const processingOrders = sampleOrders.filter(order => order.status === "Processing").length;
  const shippedOrders = sampleOrders.filter(order => order.status === "Shipped").length;
  const deliveredOrders = sampleOrders.filter(order => order.status === "Delivered").length;
  const cancelledOrders = sampleOrders.filter(order => order.status === "Cancelled").length;
  const totalRevenue = sampleOrders.reduce((sum, order) => sum + parseFloat(order.amount.replace("$", "")), 0).toFixed(2);

  // Data for Status Distribution Pie Chart
  const statusData = [
    { name: 'Processing', value: processingOrders },
    { name: 'Shipped', value: shippedOrders },
    { name: 'Delivered', value: deliveredOrders },
    { name: 'Cancelled', value: cancelledOrders },
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']; // Sample colors

  // Add state for delivery dialog
  const [showDeliveryDialog, setShowDeliveryDialog] = useState(false);
  const [selectedOrderForDelivery, setSelectedOrderForDelivery] = useState(null);

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      {/* Order Statistics Cards */}
      

      {/* Recent Orders Table - Full Width */}
      <div className="grid gap-4 mt-4">{/* New grid for full width table */}
        <div className="col-span-full">{/* Table container spans full width */}
          <div className="rounded-lg border p-4">
            <h3 className="text-lg font-medium">Recent Orders</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order ID</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sampleOrders.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell>{order.id}</TableCell>
                    <TableCell>{order.customer}</TableCell>
                    <TableCell>{order.amount}</TableCell>
                    <TableCell>{order.status}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {/* Cancel Order Dialog */}
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">Cancel</Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[425px]">
                            <DialogHeader>
                              <DialogTitle>Cancel Order {order.id}</DialogTitle>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                              <p>Please provide a reason for cancellation</p>
                              <div className="grid gap-2">
                                <Label htmlFor="reason">Reason for Cancellation</Label>
                                <Select>
                                  <SelectTrigger id="reason">
                                    <SelectValue placeholder="Select reason" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                                    <SelectItem value="customer_request">Customer Request</SelectItem>
                                    <SelectItem value="delivery_issue">Delivery Issue</SelectItem>
                                    <SelectItem value="payment_issue">Payment Issue</SelectItem>
                                    <SelectItem value="other">Other</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div className="grid gap-2">
                                <Label htmlFor="comments">Additional Comments</Label>
                                <Textarea id="comments" placeholder="Enter additional details..." />
                              </div>
                            </div>
                            <DialogFooter>
                              <DialogClose asChild>
                                <Button type="button" variant="secondary">
                                  Close
                                </Button>
                              </DialogClose>
                              <Button type="submit">Cancel Order</Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>

                        {/* Process Order Dialog */}
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">Process</Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[425px]">
                            <DialogHeader>
                              <DialogTitle>Process Order {order.id}</DialogTitle>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                              <div className="grid gap-2">
                                <Label htmlFor="customer">Customer</Label>
                                <Input id="customer" value={order.customer} readOnly />
                              </div>
                              <div className="grid gap-2">
                                <Label htmlFor="amount">Amount</Label>
                                <Input id="amount" value={order.amount} readOnly />
                              </div>
                              <div className="grid gap-2">
                                <Label htmlFor="orderDate">Order Date</Label>
                                <Input id="orderDate" value={order.order_date} readOnly />
                              </div>
                              <div className="grid gap-2">
                                <Label htmlFor="shopId">Shop ID</Label>
                                <Input id="shopId" value={order.shop_id} readOnly />
                              </div>
                              <div className="grid gap-2">
                                <Label htmlFor="status">Status</Label>
                                <Select value={order.status}> {/* Pre-populate with sample data */}
                                  <SelectTrigger id="status">
                                    <SelectValue placeholder="Select status" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="Processing">Processing</SelectItem>
                                    <SelectItem value="Shipped">Shipped</SelectItem>
                                    <SelectItem value="Delivered">Delivered</SelectItem>
                                    <SelectItem value="Cancelled">Cancelled</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              {/* Placeholder for Item Details */}
                              <div className="grid gap-2">
                                <h4 className="text-md font-medium">Items</h4>
                                {order.items.map((item, index) => (
                                  <div key={index} className="flex justify-between">
                                    <span>{item.item_name} (x{item.quantity})</span>
                                    <span>{item.price}</span>
                                  </div>
                                ))}
                              </div>
                              
                            </div>
                            <DialogFooter>
                              <DialogClose asChild>
                                <Button type="button" variant="secondary">
                                  Cancel
                                </Button>
                              </DialogClose>
                              <Button type="submit">Save Changes</Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

     
      
    </div>
  );
}
