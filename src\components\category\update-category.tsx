import { Pencil } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";

interface UpdateCategoryProps {
    categoryId:number;
    categoryName:string;
    onUpdate: (categoryId: number, categoryName: string) => void;
}

export default function UpdateCategory({categoryId,categoryName,onUpdate}:UpdateCategoryProps) {

    function handleUpdate(e: React.FormEvent<HTMLFormElement>){
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        
        const categoryName = formData.get("categoryName") as string;

        onUpdate(categoryId, categoryName);
    }

    return (
        <>
            <Dialog>
            <DialogTrigger asChild className="cursor-pointer">
                <button className="bg-transparent cursor-pointer"><Pencil className="text-yellow-400 size-4" /></button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Update category</DialogTitle>
                    <DialogDescription>
                        Update a category in your list here.
                    </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleUpdate}>
                    <div className="grid gap-4 py-4">
                        <Label htmlFor="shopName" className="text-right"> Category ID :- {categoryId} </Label>
                        <div className="flex flex-col gap-4">
                            <Label htmlFor="categoryName" className="text-right"> Category Name :-</Label>
                            <Input id="categoryName" name="categoryName" placeholder="Enter Category Name" className="col-span-3" defaultValue={categoryName} />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button type="reset">Clear Form</Button>
                        <Button type="submit">Update Shop</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
        </>
    )
}