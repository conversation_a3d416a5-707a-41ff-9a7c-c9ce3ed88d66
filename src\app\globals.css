@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

:root {
    --background: #ffffff; /* Replaces oklch(1 0 0) - white */
    --foreground: #000000; /* Replaces oklch(0.145 0 0) - black */
    --card: #ffffff; /* Replaces oklch(1 0 0) */
    --card-foreground: #000000; /* Replaces oklch(0.145 0 0) */
    --popover: #ffffff; /* Replaces oklch(1 0 0) */
    --popover-foreground: #000000; /* Replaces oklch(0.145 0 0) */
    --primary: #333333; /* Replaces oklch(0.205 0 0) - dark gray */
    --primary-foreground: #ffffff; /* Replaces oklch(0.985 0 0) - near white */
    --secondary: #f5f5f5; /* Replaces oklch(0.97 0 0) - light gray */
    --secondary-foreground: #333333; /* Replaces oklch(0.205 0 0) */
    --muted: #f5f5f5; /* Replaces oklch(0.97 0 0) */
    --muted-foreground: #6b7280; /* Replaces oklch(0.556 0 0) - gray */
    --accent: #f5f5f5; /* Replaces oklch(0.97 0 0) */
    --accent-foreground: #333333; /* Replaces oklch(0.205 0 0) */
    --destructive: #dc2626; /* Replaces oklch(0.577 0.245 27.325) - red */
    --destructive-foreground: #ffffff; /* Replaces oklch(0.577 0.245 27.325) - white */
    --border: #e5e7eb; /* Replaces oklch(0.922 0 0) - light gray */
    --input: #e5e7eb; /* Replaces oklch(0.922 0 0) */
    --ring: #9ca3af; /* Replaces oklch(0.708 0 0) - gray */
    --chart-1: #f59e0b; /* Replaces oklch(0.646 0.222 41.116) - amber */
    --chart-2: #10b981; /* Replaces oklch(0.6 0.118 184.704) - green */
    --chart-3: #3b82f6; /* Replaces oklch(0.398 0.07 227.392) - blue */
    --chart-4: #22c55e; /* Replaces oklch(0.828 0.189 84.429) - green */
    --chart-5: #eab308; /* Replaces oklch(0.769 0.188 70.08) - yellow */
    --radius: 0.625rem;
    --sidebar: #ffffff; /* Replaces oklch(0.985 0 0) */
    --sidebar-foreground: #000000; /* Replaces oklch(0.145 0 0) */
    --sidebar-primary: #333333; /* Replaces oklch(0.205 0 0) */
    --sidebar-primary-foreground: #ffffff; /* Replaces oklch(0.985 0 0) */
    --sidebar-accent: #f5f5f5; /* Replaces oklch(0.97 0 0) */
    --sidebar-accent-foreground: #333333; /* Replaces oklch(0.205 0 0) */
    --sidebar-border: #e5e7eb; /* Replaces oklch(0.922 0 0) */
    --sidebar-ring: #9ca3af; /* Replaces oklch(0.708 0 0) */
}

.dark {
    --background: #000000; /* Replaces oklch(0.145 0 0) - black */
    --foreground: #ffffff; /* Replaces oklch(0.985 0 0) - white */
    --card: #000000; /* Replaces oklch(0.145 0 0) */
    --card-foreground: #ffffff; /* Replaces oklch(0.985 0 0) */
    --popover: #000000; /* Replaces oklch(0.145 0 0) */
    --popover-foreground: #ffffff; /* Replaces oklch(0.985 0 0) */
    --primary: #ffffff; /* Replaces oklch(0.985 0 0) */
    --primary-foreground: #333333; /* Replaces oklch(0.205 0 0) */
    --secondary: #1f2937; /* Replaces oklch(0.269 0 0) - dark gray */
    --secondary-foreground: #ffffff; /* Replaces oklch(0.985 0 0) */
    --muted: #1f2937; /* Replaces oklch(0.269 0 0) */
    --muted-foreground: #9ca3af; /* Replaces oklch(0.708 0 0) - gray */
    --accent: #1f2937; /* Replaces oklch(0.269 0 0) */
    --accent-foreground: #ffffff; /* Replaces oklch(0.985 0 0) */
    --destructive: #b91c1c; /* Replaces oklch(0.396 0.141 25.723) - dark red */
    --destructive-foreground: #fee2e2; /* Replaces oklch(0.637 0.237 25.331) - light red */
    --border: #1f2937; /* Replaces oklch(0.269 0 0) */
    --input: #1f2937; /* Replaces oklch(0.269 0 0) */
    --ring: #6b7280; /* Replaces oklch(0.556 0 0) - gray */
    --chart-1: #7c3aed; /* Replaces oklch(0.488 0.243 264.376) - purple */
    --chart-2: #2dd4bf; /* Replaces oklch(0.696 0.17 162.48) - teal */
    --chart-3: #eab308; /* Replaces oklch(0.769 0.188 70.08) - yellow */
    --chart-4: #a855f7; /* Replaces oklch(0.627 0.265 303.9) - purple */
    --chart-5: #ef4444; /* Replaces oklch(0.645 0.246 16.439) - red */
    --sidebar: #333333; /* Replaces oklch(0.205 0 0) */
    --sidebar-foreground: #ffffff; /* Replaces oklch(0.985 0 0) */
    --sidebar-primary: #7c3aed; /* Replaces oklch(0.488 0.243 264.376) */
    --sidebar-primary-foreground: #ffffff; /* Replaces oklch(0.985 0 0) */
    --sidebar-accent: #1f2937; /* Replaces oklch(0.269 0 0) */
    --sidebar-accent-foreground: #ffffff; /* Replaces oklch(0.985 0 0) */
    --sidebar-border: #1f2937; /* Replaces oklch(0.269 0 0) */
    --sidebar-ring: #4b5563; /* Replaces oklch(0.439 0 0) */
}

/* Rest of the file remains unchanged */
.theme-login-one {
    --primary: #ce2a2d;
    --primary-foreground: #fff;
    --ring: #ce2a2d9c;
    --radius: 0rem;
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);

    font-family: var(--font-sans);

    a {
        color: var(--primary);
    }

    [data-slot="card"] {
        border-radius: 0rem;
        box-shadow: none;
    }
}

.theme-login-two {
    --primary: #035fa8;
    --primary-foreground: #fff;
    --ring: #035fa89c;
    font-family: var(--font-serif);

    a {
        color: var(--primary);
    }
}

.theme-login-three {
    --primary: #22c55e;
    --primary-foreground: #000;
    --ring: #22c55e;
    --radius: 1.5rem;

    font-family: var(--font-manrope);

    a {
        color: var(--primary);
    }

    [data-slot="card"] {
        @apply shadow-xl;
    }

    [data-slot="input"] {
        @apply dark:bg-input;
    }
}

@theme inline {
    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
    --animate-accordion-down: accordion-down 0.2s ease-out;
    --animate-accordion-up: accordion-up 0.2s ease-out;

    @keyframes accordion-down {
        from {
            height: 0;
        }
        to {
            height: var(--radix-accordion-content-height);
        }
    }

    @keyframes accordion-up {
        from {
            height: var(--radix-accordion-content-height);
        }
        to {
            height: 0;
        }
    }
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
    }
}