"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useRef, useState } from "react";
import { toast } from "sonner";
import {jsPDF} from "jspdf";
import html2canvas from "html2canvas";
import {v4 as uuidv4} from "uuid";
import { InvoiceData } from "@/components/types/invoice";
import { InvoiceForm } from "@/components/invoice/invoice-form";
import InvoicePreview from "@/components/invoice/invoice-preview";



export default function InvoicesPage() {
    const [activeTab, setActiveTab] = useState("edit");
    const invoiceRef = useRef<HTMLDivElement>(null);

    const [invoiceData, setInvoiceData] = useState<InvoiceData>({
        invoiceNumber: `INV-${Math.floor(Math.random() * 10000)}`,
        date: new Date().toISOString().split("T")[0],
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
            .toISOString()
            .split("T")[0],
        companyName: "Ruwan Distributors",
        companyLogo: "",
        companyDetails: "Ruwan Distribution is an island wide distributor.",
        fromName: "Ruwan Distributors",
        fromEmail: "<EMAIL>",
        fromAddress: "No: 123, Main Street, Colombo, Sri Lanka",
        toName: "",
        toEmail: "",
        toAddress: "",
        lineItems:[],
        notes: "",
        taxRate: 0,
        currency: "LKR",
        footer: "Thank you for your business!",
        discountType: "percentage",
        discountValue: 0,
        applyInvoiceDiscountToDiscountedItems: true,
    });

    const calculateSubtotal = () => {
        return invoiceData.lineItems.reduce((sum, item) => sum + calculateItemTotal(item), 0);
    };

    const calculateItemDiscount = (item: (typeof invoiceData.lineItems)[0]) => {
        const itemSubtotal = item.quantity * item.price;
        if (item.discountValue <= 0) return 0;

        if (item.discountType === "percentage") {
            return itemSubtotal * (item.discountValue / 100);
        } else {
            return Math.min(item.discountValue, itemSubtotal); // Ensure discount doesn't exceed item subtotal
        }
    };

    const handleInvoiceChange = (field: string, value: string | number | boolean) => {
        if (field === "currency") {
            // When invoice currency changes, update all items with the same currency to have exchange rate 1
            const updatedItems = invoiceData.lineItems.map((item) => {
                if (item.currency === invoiceData.currency) {
                    return {...item, currency: value as string, exchangeRate: 1};
                }
                return item;
            });
            setInvoiceData({...invoiceData, [field]: value, items: updatedItems});
        } else {
            setInvoiceData(prevData => ({...prevData, [field]: value}));            
        }
    };

    const handleItemChange = (id: string, field: string, value: string | number) => {
        console.log(invoiceData.lineItems);
        
        console.log({ id, field, value });
        
        const updatedItems = invoiceData.lineItems.map((item) => {
            if (item.itemCode === id) {
                if (field === "currency") {
                    // If currency is changed to match invoice currency, reset exchange rate to 1
                    const exchangeRate = value === invoiceData.currency ? 1 : item.exchangeRate;
                    return {...item, [field]: value as string, exchangeRate};
                }

                if (field === "quantity" || field === "price" || field === "exchangeRate" || field === "discountValue") {
                    return {...item, [field]: Number(value) || 0};
                }

                return {...item, [field]: value};
            }
            return item;
        });
        setInvoiceData({...invoiceData, lineItems: updatedItems});
    };

    const calculateTotalItemDiscounts = () => {
        return invoiceData.lineItems.reduce((sum, item) => {
            const itemDiscount = calculateItemDiscount(item);
            // Convert to invoice currency if needed
            return (sum + (item.currency === invoiceData.currency ? itemDiscount : itemDiscount * item.exchangeRate));
        }, 0);
    };

    const addItem = () => {
        const newItem = {
            itemCode: uuidv4(),
            itemName: "",
            category: "",
            quantity: 1,
            price: 0,
            currency: invoiceData.currency,
            exchangeRate: 1,
            discountType: "percentage",
            discountValue: 0,
        };

        // Create a new array reference to trigger re-render
        setInvoiceData(prevData => ({
            ...prevData,
            lineItems: [...prevData.lineItems, newItem as typeof prevData.lineItems[0]],
        }));
    };

    const removeItem = (id: string) => {
        if (invoiceData.lineItems.length > 1) {
            setInvoiceData({
                ...invoiceData, lineItems: invoiceData.lineItems.filter((item) => item.itemCode !== id),
            });
        } else {
            toast.error("Cannot remove the last item.");
        }
    };

    const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onloadend = () => {
                setInvoiceData({
                    ...invoiceData, companyLogo: reader.result as string,
                });
            };
            reader.readAsDataURL(file);
        }
    };

    const calculateDiscount = () => {
        if (invoiceData.discountValue <= 0) return 0;

        let discountableAmount = 0;

        if (invoiceData.applyInvoiceDiscountToDiscountedItems) {
            // Apply discount to all items
            discountableAmount = calculateSubtotal();
        } else {
            // Apply discount only to items without their own discount
            discountableAmount = invoiceData.lineItems.reduce((sum, item) => {
                if (item.discountValue > 0) return sum; // Skip items with discount

                const itemTotal = item.quantity * item.price;
                return (sum + (item.currency === invoiceData.currency ? itemTotal : itemTotal * item.exchangeRate));
            }, 0);
        }

        if (invoiceData.discountType === "percentage") {
            return discountableAmount * (invoiceData.discountValue / 100);
        } else {
            return Math.min(invoiceData.discountValue, discountableAmount); // Ensure discount doesn't exceed subtotal
        }
    };

    const calculateItemTotal = (item: (typeof invoiceData.lineItems)[0]) => {
        const itemSubtotal = item.quantity * item.price;
        const itemDiscount = calculateItemDiscount(item);
        const itemNetTotal = itemSubtotal - itemDiscount;

        return item.currency === invoiceData.currency ? itemNetTotal : itemNetTotal * item.exchangeRate;
    };

    const calculateTaxableAmount = () => {
        return calculateSubtotal() - calculateDiscount();
    };

    const calculateTax = () => {
        return calculateTaxableAmount() * (invoiceData.taxRate / 100);
    };

    const calculateTotal = () => {
        return calculateTaxableAmount() + calculateTax();
    };

    const downloadPdf = async () => {
        // try {
        //     const invoicePayload = {
        //         customerName: invoiceData.toName,
        //         items: invoiceData.items.map(item => item.id),
        //         date: invoiceData.date,
        //         amount: calculateTotal(),
        //         currency: invoiceData.currency,
        //         discount: calculateDiscount(),
        //         tax: calculateTax(),
        //     };

        //     const response = await fetch("/api/invoice/create", {
        //         method: "POST", headers: {
        //             "Content-Type": "application/json",
        //         }, body: JSON.stringify(invoicePayload),
        //     });

        //     if (response.ok) {
        //         toast.success("Invoice saved successfully!");
        //     } else {
        //         console.log("Error : ", response);
        //         toast.error("Failed to save invoice!");
        //     }
        // } catch (error) {
        //     console.error("Error sending invoice data to backend", error);
        //     toast.error("An unexpected error occurred.");
        // }


        if (invoiceRef.current) {
            const canvas = await html2canvas(invoiceRef.current, {
                scale: 2, useCORS: true, logging: false,
            });

            const imgData = canvas.toDataURL("image/png");
            const pdf = new jsPDF({
                orientation: "portrait", unit: "mm", format: "a4",
            });

            const imgWidth = 210;
            const imgHeight = (canvas.height * imgWidth) / canvas.width;

            pdf.addImage(imgData, "PNG", 0, 0, imgWidth, imgHeight);
            const pdfName = `invoice-${invoiceData.invoiceNumber || Date.now()}.pdf`;
            pdf.save(pdfName);
        }
    };
    
    return (
        <div className="space-y-4">
            <div className="flex flex-row gap-6 items-center">
            </div>
            <div className="mx-6">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-2 mb-6">
                        <TabsTrigger value="edit">Edit Invoice</TabsTrigger>
                        <TabsTrigger value="preview">Preview</TabsTrigger>
                    </TabsList>

                    <TabsContent value="edit">
                        <InvoiceForm
                            invoiceData={invoiceData}
                            setInvoiceData={setInvoiceData}
                            handleInvoiceChange={handleInvoiceChange}
                            handleItemChange={handleItemChange}
                            handleLogoUpload={handleLogoUpload}
                            // addItem={addItem}
                            removeItem={removeItem}
                            calculateItemDiscount={calculateItemDiscount}
                            calculateItemTotal={calculateItemTotal}
                            calculateTotalItemDiscounts={calculateTotalItemDiscounts}
                            calculateSubtotal={calculateSubtotal}
                            calculateDiscount={calculateDiscount}
                            calculateTaxableAmount={calculateTaxableAmount}
                            calculateTax={calculateTax}
                            calculateTotal={calculateTotal}
                        />
                    </TabsContent>

                    <TabsContent value="preview">
                        <Card className="p-6">
                            <div ref={invoiceRef}>
                                <InvoicePreview
                                    invoiceData={invoiceData}
                                    calculateItemDiscount={calculateItemDiscount}
                                    calculateItemTotal={calculateItemTotal}
                                    calculateTotalItemDiscounts={calculateTotalItemDiscounts}
                                    calculateSubtotal={calculateSubtotal}
                                    calculateDiscount={calculateDiscount}
                                    calculateTaxableAmount={calculateTaxableAmount}
                                    calculateTax={calculateTax}
                                    calculateTotal={calculateTotal}
                                />
                            </div>
                            <div className="mt-6 flex justify-end">
                                <Button onClick={downloadPdf}>Download PDF</Button>
                            </div>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
            
            {/* <div>
                <div>
                    <DataTable columns={columns} data={currentItems} />
                </div>
            </div>
            <div className="flex items-center justify-end space-x-2 py-4">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleChangePage(currentPage - 1)}
                    disabled={currentPage === 1}
                >
                    Previous
                </Button>
                <span>Page {currentPage} of {totalPages}</span>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleChangePage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                >
                    Next
                </Button>
            </div> */}
        </div>
    )
}