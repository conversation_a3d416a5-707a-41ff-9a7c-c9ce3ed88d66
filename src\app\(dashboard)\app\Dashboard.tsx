
"use client"
import { TopNav } from '@/components/admin/top-nav'
import { AppSidebar } from '@/components/app/side-bar'
import React, { useState } from 'react'

interface DashboardProps{
  children:React.ReactNode;
}

const Dashboard = ({children}:DashboardProps) => {
  const [headerName, setHeaderName] = useState("Overview");
  return (
    <div className="min-h-screen flex w-full">
            <AppSidebar setHeaderName={setHeaderName}/>
            <div className="flex-1 flex flex-col overflow-hidden">
                <TopNav navName={headerName}/>
                <main className="flex-1 p-6 overflow-y-auto w-full">
                    {children}
                </main>
            </div>
        </div>
  )
}

export default Dashboard