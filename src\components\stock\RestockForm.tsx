'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectTrigger,
    SelectValue,
    SelectContent,
    SelectItem,
} from '@/components/ui/select';

interface Product {
    id: string;
    name: string;
}

interface StockItem {
    stock_id: number;
    item_name: string;
    category_id: number;
    category: {
        category_id: number;
        category_name: string;
    };
    quantity: number;
    price: number;
    date_added: string;
    is_main_stock: boolean;
}

export default function RestockForm() {
    const [products, setProducts] = useState<Product[]>([]);
    const [productName, setProductName] = useState('');
    const [category, setCategory] = useState('');
    const [quantity, setRestockQuantity] = useState('');
    const [unitPrice, setUnitPrice] = useState('');
    const [dateAdded, setDateAdded] = useState('');
    const [isMainStock, setIsMainStock] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [errors, setErrors] = useState<{[key: string]: string}>({});

    // Validation function
    const validateForm = () => {
        const newErrors: {[key: string]: string} = {};
        
        // Product Name validation
        if (!productName) {
            newErrors.productName = 'Please select a product';
        }
        
        // Quantity validation - required and must be a positive number
        if (!quantity) {
            newErrors.quantity = 'Quantity is required';
        } else if (isNaN(Number(quantity)) || Number(quantity) <= 0) {
            newErrors.quantity = 'Quantity must be a positive number';
        }
        
        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle quantity change with validation
    const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // Only allow positive numbers
        if (value === '' || (!isNaN(Number(value)) && Number(value) >= 0)) {
            setRestockQuantity(value);
            if (errors.quantity) {
                setErrors(prev => ({ ...prev, quantity: '' }));
            }
        }
    };

    // Fetch products on component mount
    useEffect(() => {
        const fetchProducts = async () => {
            try {
                const response = await fetch('/api/stock/read');
                if (response.ok) {
                    const data = await response.json();
                    // Transform stock items to product format
                    const productList = data.map((item: StockItem) => ({
                        id: item.stock_id.toString(),
                        name: item.item_name
                    }));
                    setProducts(productList);
                } else {
                    console.error('Failed to fetch products: Server returned', response.status);
                }
            } catch (error) {
                console.error('Failed to fetch products:', error);
            }
        };

        fetchProducts();
    }, []);

    // Fetch product details when a product is selected
    const handleProductSelect = async (selectedProductName: string) => {
        setProductName(selectedProductName);
        
        if (!selectedProductName) return;
        
        try {
            setIsLoading(true);
            console.log(`Fetching details for: ${selectedProductName}`);
            const response = await fetch(`/api/stock/search?item_name=${encodeURIComponent(selectedProductName)}`);
            
            if (response.ok) {
                const data = await response.json();
                console.log('Search API response:', data);
                
                if (data.length > 0) {
                    const stockItem = data[0]; // Get the first matching item
                    console.log('Selected stock item:', stockItem);
                    
                    // Populate form fields with the stock item data
                    setCategory(stockItem.category.category_name);
                    setUnitPrice(stockItem.price.toString());
                    
                    // Format date to YYYY-MM-DD for input type="date"
                    const date = new Date(stockItem.date_added);
                    const formattedDate = date.toISOString().split('T')[0];
                    setDateAdded(formattedDate);
                    
                    setIsMainStock(stockItem.is_main_stock ? 'Main Stock' : 'Sub Stock');
                    
                    // Don't set quantity as this will be the restock amount
                    setRestockQuantity('');
                } else {
                    console.log('No items found in search results');
                }
            } else {
                console.error('Failed to fetch product details, status:', response.status);
                const errorText = await response.text();
                console.error('Error response:', errorText);
            }
        } catch (error) {
            console.error('Error fetching product details:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleRestock = async () => {
        // Validate form before submission
        if (!validateForm()) {
            return;
        }

        try {
            setIsLoading(true);
            
            // First, get the stock_id for the selected product
            const searchResponse = await fetch(`/api/stock/search?item_name=${encodeURIComponent(productName)}`);
            if (!searchResponse.ok) {
                throw new Error('Failed to find product details');
            }
            
            const searchData = await searchResponse.json();
            if (searchData.length === 0) {
                throw new Error('Product not found');
            }
            
            const stockItem = searchData[0];
            const stockId = stockItem.stock_id;
            
            // Calculate new quantity (current + restock amount)
            const newQuantity = stockItem.quantity + Number(quantity);
            
            // Call the update API
            const updateResponse = await fetch('/api/stock/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    stock_id: stockId,
                    quantity: newQuantity,
                }),
            });
            
            if (!updateResponse.ok) {
                const errorData = await updateResponse.json();
                throw new Error(errorData.error || 'Failed to update stock');
            }
            
            // Success
            alert(`Successfully restocked ${productName} with ${quantity} units`);
            
            // Reset form
            setProductName('');
            setCategory('');
            setRestockQuantity('');
            setUnitPrice('');
            setDateAdded('');
            setIsMainStock('');
            setErrors({});
            
        } catch (error) {
            console.error('Error restocking item:', error);
            alert(error instanceof Error ? error.message : 'Failed to restock item');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="flex justify-center pt-6">
            <div className="w-full max-w-2xl h-lg p-8 border rounded-xl shadow-md flex flex-col justify-between">
                <div className="m-4">
                    <h2 className="text-xl font-semibold mb-3">Restock Inventory</h2>
                    <p className="text-lg text-muted-foreground mb-4">
                        Fill in all the stock details to restock inventory.
                    </p>

                    <div className="grid gap-6">
                        <div className="mt-2">
                            <Label htmlFor="productName">Product Name</Label>
                            <div className="mt-2">
                                <Select value={productName} onValueChange={handleProductSelect}>
                                    <SelectTrigger id="productName" className="w-full">
                                        <SelectValue placeholder="Select a product" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {products.length === 0 ? (
                                            <SelectItem value="loading" disabled>Loading products...</SelectItem>
                                        ) : (
                                            products.map(product => (
                                                <SelectItem key={product.id} value={product.name}>
                                                    {product.name}
                                                </SelectItem>
                                            ))
                                        )}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <div className="mt-2">
                            <Label htmlFor="category">Category</Label>
                            <div className="mt-2">
                                <Input id="category" value={category} readOnly />
                            </div>
                        </div>

                        <div className="mt-2">
                            <Label htmlFor="restockQty">Quantity to Add</Label>
                            <div className="mt-2">
                                <Input
                                    id="restockQty"
                                    type="number"
                                    value={quantity}
                                    onChange={handleQuantityChange}
                                />
                                {errors.quantity && <div className="text-red-500 text-sm">{errors.quantity}</div>}
                            </div>
                        </div>

                        <div className="mt-2">
                            <Label htmlFor="unitPrice">Unit Price</Label>
                            <div className="mt-2">
                                <Input
                                    id="unitPrice"
                                    type="number"
                                    value={unitPrice}
                                    readOnly
                                />
                            </div>
                        </div>

                        <div className="mt-2">
                            <Label htmlFor="dateAdded">Date Added</Label>
                            <div className="mt-2">
                                <Input
                                    id="dateAdded"
                                    type="date"
                                    value={dateAdded}
                                    readOnly
                                />
                            </div>
                        </div>

                        <div className="mt-2">
                            <Label htmlFor="isMainStock">Stock Type</Label>
                            <div className="mt-2">
                                <Input
                                    id="isMainStock"
                                    value={isMainStock}
                                    readOnly
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div className="mt-8 flex justify-between gap-4">
                    <Button onClick={handleRestock} disabled={isLoading}>
                        {isLoading ? 'Loading...' : 'Submit Restock'}
                    </Button>
                </div>
            </div>
        </div>
    );
}
