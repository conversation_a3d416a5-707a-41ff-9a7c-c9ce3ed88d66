import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(req: NextRequest) {
  try {
    const stocks = await prisma.stock.findMany({
      include: {
        category: true, // include category details if you want
      },
      orderBy: {
        item_name: 'asc',
      },
    });
    return NextResponse.json(stocks, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch stocks', details: String(error) }, { status: 500 });
  }
}