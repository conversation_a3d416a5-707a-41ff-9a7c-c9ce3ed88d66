import { NextResponse } from "next/server";
import { prisma } from "@/prisma";

export async function POST(request: Request) {
  try {
    const { categoryId } = await request.json();

    if (!categoryId || typeof categoryId !== "number") {
      return NextResponse.json({ error: "Invalid category ID" }, { status: 400 });
    }

    await prisma.category.delete({
      where: { category_id:categoryId },
    });

    return NextResponse.json({ message: "Category deleted successfully" }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: "Failed to delete category" }, { status: 500 });
  }
}
