import { NextResponse } from "next/server";
import { prisma } from "@/prisma";

export async function GET() {
  try {
    const routes = await prisma.route.findMany({
      orderBy: {
        route_code: 'asc',
      },
    });

    // Transform the data to match frontend expectations
    const transformedRoutes = routes.map(route => ({
      routeId: route.route_id,
      routeCode: route.route_code,
      description: route.description
    }));

    return NextResponse.json(transformedRoutes, { status: 200 });
  } catch (error) {
    console.error("Error fetching routes:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
