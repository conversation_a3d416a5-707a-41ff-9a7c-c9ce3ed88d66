"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Truck} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function DeliveryForm() {
  // Sample order data for the dropdown
  const sampleOrders = [
    { id: "ORD001", customer: "<PERSON>" },
    { id: "ORD002", customer: "<PERSON>" },
    { id: "ORD003", customer: "<PERSON>" },
    { id: "ORD004", customer: "<PERSON>" },
    { id: "ORD005", customer: "<PERSON>" },
  ]

  // State variables for form inputs
  const [orderId, setOrderId] = useState("")
  const [receiverName, setReceiverName] = useState("")
  const [phoneNumber, setPhoneNumber] = useState("")
  const [address, setAddress] = useState("")
  const [city, setCity] = useState("")
  const [postalCode, setPostalCode] = useState("")
  const [deliveryDate, setDeliveryDate] = useState("")

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault()
    
    // Log all form data including the order ID
    console.log("Order ID:", orderId)
    console.log("Receiver Name:", receiverName)
    console.log("Phone Number:", phoneNumber)
    console.log("Address:", address)
    console.log("City:", city)
    console.log("Postal Code:", postalCode)
    console.log("Delivery Date:", deliveryDate)
  }

  // Handle form reset/cancel
  const handleCancel = () => {
    setReceiverName("")
    setPhoneNumber("")
    setAddress("")
    setCity("")
    setPostalCode("")
    setDeliveryDate("")
  }

  return (
    <div className="max-w-xl mx-auto p-8 rounded-lg shadow-md border bg-card">
      <div className="flex items-center gap-2 mb-4">
        <Truck className="text-blue-600" />
        <h2 className="text-2xl font-bold text-foreground">Add New Delivery</h2>
      </div>
      <p className="text-muted-foreground mb-6">Enter details for the new delivery order.</p>

      <form className="space-y-6" onSubmit={handleSubmit}>
        {/* Order ID Dropdown - Adjusted to match other input field sizes */}
        <div>
          <Label htmlFor="orderId" className="mb-3">Select Order</Label>
          <Select value={orderId} onValueChange={setOrderId}>
            <SelectTrigger id="orderId" className="w-full">
              <SelectValue placeholder="Select an order" />
            </SelectTrigger>
            <SelectContent>
              {sampleOrders.map((order) => (
                <SelectItem key={order.id} value={order.id}>
                  {order.id} - {order.customer}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="name" className="mb-3">Receiver Name</Label>
          <Input
            id="name"
            value={receiverName}
            onChange={(e) => setReceiverName(e.target.value)}
          />
        </div>

        <div>
          <Label htmlFor="phone" className="mb-3">Phone Number</Label>
          <Input
            id="phone"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
          />
        </div>

        <div>
          <Label htmlFor="address" className="mb-3">Delivery Address</Label>
          <Textarea
            id="address"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="city" className="mb-3">City</Label>
            <Input
              id="city"
              value={city}
              onChange={(e) => setCity(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="postalCode" className="mb-3">Postal Code</Label>
            <Input
              id="postalCode"
              value={postalCode}
              onChange={(e) => setPostalCode(e.target.value)}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="date" className="mb-3">Delivery Date</Label>
          <Input
            id="date"
            type="date"
            value={deliveryDate}
            onChange={(e) => setDeliveryDate(e.target.value)}
          />
        </div>

        <div className="flex justify-between pt-4">
          <Button type="submit" size="sm" className="w-1/4">
            <Truck className="mr-2 h-4 w-4" />
            Schedule
          </Button>
          <Button 
            variant="outline" 
            type="button" 
            size="sm" 
            className="w-1/4"
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  )
}
