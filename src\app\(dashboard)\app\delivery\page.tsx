
"use client"

import { useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import RecentDeliveries from "@/components/delivery/get-recent-deliveries";
import DeliveryForm from "@/components/delivery/delivery-management";
import { Button } from "@/components/ui/button";

export default function DeliveryPage() {
    const searchParams = useSearchParams();
    const [showForm, setShowForm] = useState(false);
    
    // Check if showForm parameter is present in the URL
    useEffect(() => {
        if (searchParams.get("showForm") === "true") {
            setShowForm(true);
        }
    }, [searchParams]);
    
    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <div>
                    {!showForm ? (
                        <Button onClick={() => setShowForm(true)}>
                            Process New Delivery
                        </Button>
                    ) : (
                        <Button variant="outline" onClick={() => setShowForm(false)}>
                            View Recent Deliveries
                        </Button>
                    )}
                </div>
            </div>
            
            {showForm ? (
                <DeliveryForm />
            ) : (
                <RecentDeliveries />
            )}
        </div>
    );
}
