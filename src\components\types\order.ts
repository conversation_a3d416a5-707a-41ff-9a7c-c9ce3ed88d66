export interface Order {
  id: string
  shop: string
  shopCode: string
  items: number
  total: number
  status: "delivered" | "pending" | "cancelled" | "processing"
  payment: "cash" | "credit" | "check" | "mixed"
  date: string
  deliveryDate?: string
  notes?: string
}

export interface OrderItem {
  id: string
  name: string
  category: string
  quantity: number
  unitPrice: number
  total: number
}

export interface CancellationReason {
  id: string
  reason: string
  description?: string
}

export interface OrderFilters {
  status: string
  searchTerm: string
  dateRange?: {
    from: Date
    to: Date
  }
  paymentMode?: string
}

export interface OrderStats {
  totalOrders: number
  pendingOrders: number
  deliveredToday: number
  totalValue: number
  cancelledOrders: number
}
