'use server'

import { auth } from "@/auth"
import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"

export async function deleteShop(shopId: number) {
  try {
    const session = await auth()
    if (!session?.user) {
      throw new Error("Unauthorized")
    }

    // Check if shop exists
    const existingShop = await prisma.shop.findUnique({
      where: { shop_id: shopId },
      include: {
        orders: {
          where: {
            status: {
              in: ['PENDING', 'PROCESSING']
            }
          }
        }
      }
    })

    if (!existingShop) {
      return { success: false, error: "Shop not found" }
    }

    // Check if shop has active orders
    if (existingShop.orders.length > 0) {
      return { 
        success: false, 
        error: "Cannot delete shop with active orders. Please complete or cancel all orders first." 
      }
    }

    // Delete the shop
    await prisma.shop.delete({
      where: { shop_id: shopId }
    })

    revalidatePath('/shops')
    return { success: true }
  } catch (error) {
    console.error("Error deleting shop:", error)
    return { success: false, error: "Failed to delete shop" }
  }
} 