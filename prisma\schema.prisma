datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Account {
  id                String  @id @default(cuid())
  userId            Int     @map("user_id")
  type              String
  provider          String
  providerAccountId String  @default("") @map("provider_account_id")
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [user_id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       Int      @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [user_id], onDelete: Cascade)

  @@map("sessions")
}

enum Role {
  ADMIN
  USER
}

model User {
  user_id Int @id @default(autoincrement()) @map("user_id")
  username       String  @unique
  role           String  @default("USER")
  password       String
  categoryshop   String  @default("")
  shop_code      String  @default("")
  name           String  @default("")
  shop_id        Int?
  can_register   Boolean @default(false)
  contact_number String  @default("")
  email          String?
  category_id    Int?
  route_id       Int?
  service_area   String  @default("")
  registration_number String @default("")
  
  // Relations remain the same
  accounts       Account[]
  sessions       Session[]
  verificationTokens VerificationToken[]
  passwordResetTokens PasswordResetToken[]

  shop           Shop?    @relation(fields: [shop_id], references: [shop_id])
  category       Category? @relation(fields: [category_id], references: [category_id])
  route          Route?   @relation(fields: [route_id], references: [route_id])
}

model Shop {
  shop_id        Int      @id @default(autoincrement())
  shop_code      String
  name           String
  service_area   String
  registration_number String
  contact_number String   @default("")
  email          String   @default("")
  route_id       Int
  category_id    Int

  users          User[]
  orders         Order[]
  route          Route     @relation(fields: [route_id], references: [route_id])
  category       Category  @relation(fields: [category_id], references: [category_id])
}

model Category {
  category_id    Int      @id @default(autoincrement())
  category_name  String

  shops          Shop[]
  users          User[]
  stocks         Stock[]
}

model Route {
  route_id       Int      @id @default(autoincrement())
  route_code     String
  description    String

  shops          Shop[]
  users          User[]
}

model Order {
  order_id       Int      @id @default(autoincrement())
  shop_id        Int
  stock_id       Int
  quantity       Int
  status         String
  reason         String?
  order_date     DateTime

  shop           Shop     @relation(fields: [shop_id], references: [shop_id])
  stock          Stock    @relation(fields: [stock_id], references: [stock_id])
  invoice        Invoice?
}

model Stock {
  stock_id       Int      @id @default(autoincrement())
  item_name      String
  category_id    Int
  quantity       Int
  price          Float
  is_main_stock  Boolean
  date_added     DateTime

  category       Category @relation(fields: [category_id], references: [category_id])
  orders         Order[]
}

model Invoice {
  invoice_id     Int      @id @default(autoincrement())
  order_id       Int      @unique
  payment_mode   String
  amount         Float
  invoice_date   DateTime

  order          Order    @relation(fields: [order_id], references: [order_id])
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  User User @relation(fields: [identifier], references: [username], onDelete: Cascade)

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model PasswordResetToken {
  id      String   @id @default(cuid())
  token   String   @unique
  userId  Int
  expires DateTime
  user    User     @relation(fields: [userId], references: [user_id], onDelete: Cascade)

  @@map("password_reset_tokens")
}

