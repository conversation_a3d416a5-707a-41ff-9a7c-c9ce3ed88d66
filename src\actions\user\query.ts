"use server";

import { prisma } from "@/prisma";

export const getUserById = async (id: string) => {
  return prisma.user.findUnique({
    where: {
      user_id: Number(id),
    },
  });
};

export const getUsers = async (
  page: number,
  pageSize: number,
  search: string = ""
) => {
  const whereClause = search
    ? {
        OR: [
          { username: { contains: search } },
          { email: { contains: search } },
          { name: { contains: search } },
        ],
      }
    : {};

  const totalUsers = await prisma.user.count({ where: whereClause });
  const totalPages = Math.ceil(totalUsers / pageSize);

  const users = await prisma.user.findMany({
    where: whereClause,
    skip: (page - 1) * pageSize,
    take: pageSize,
  });

  return { users, totalPages };
};

// export const getUserAccounts = async (userId: string) => {
//   return prisma.account.findMany({
//     where: {
//       user_id: userId,
//     },
//   });
// };

// export const getUserSessions = async (userId: string) => {
//   return prisma.session.findMany({
//     where: {
//       userId: userId,
//     },
//   });
// };

export async function getUserByUsername(username: string) {
  try {
    const user = await prisma.user.findUnique({
      where: {
        username: username,
      },
      select: {
        user_id: true,
        username: true,
        password: true,
        email: true,
        role: true,
        name: true,
      },
    });

    // Map user_id to id for NextAuth compatibility
    if (user) {
      return {
        id: user.user_id.toString(), // Convert to string for NextAuth
        username: user.username,
        password: user.password,
        email: user.email,
        role: user.role,
        name: user.name,
      };
    }

    return null;
  } catch (error) {
    console.error("Database error:", error);
    return null;
  }
}
