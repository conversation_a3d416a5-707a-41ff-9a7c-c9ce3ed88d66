'use client';


import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  
} from '@/components/ui/card';
import { PackageIcon, PlusIcon } from 'lucide-react';
import { useState, useEffect } from 'react';
import AddNewItemForm from './AddNewItemForm';
import RestockForm from './RestockForm';

export default function StockManagement() {
 
  const [activeView, setActiveView] = useState<'main' | 'add-new' | 'restock'>('main');
  const [stockItems, setStockItems] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch stock items when component mounts
  useEffect(() => {
    const fetchStockItems = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/stock/read');
        if (!response.ok) {
          throw new Error('Failed to fetch stock items');
        }
        const data = await response.json();
        setStockItems(data);
      } catch (error) {
        console.error('Error fetching stock items:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStockItems();
  }, []);

  return (
    <div className='w-full h-screen flex items-center justify-center relative'>
    <Card className="w-full max-w-7xl h-full flex flex-col">

      <CardContent className='relative flex-1 flex flex-col overflow-hidden'>
        {/* Top Controls */}
        <div className="flex flex-wrap items-center gap-4 mb-6">
          
          <button 
            className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeView === 'add-new' 
                ? 'bg-white text-black border border-black' 
                : 'bg-black text-white'
            }`}
            onClick={() => setActiveView('add-new')}
          >
            <PlusIcon size={16} />
            Add New Item
          </button>

          
          <button 
            className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeView === 'restock' 
                ? 'bg-white text-black border border-black' 
                : 'bg-black text-white'
            }`}
            onClick={() => setActiveView('restock')}
          >
            <PackageIcon size={16} />
            Restock Items
          </button>

          
          {activeView !== 'main' && (
            <Button 
              variant="ghost" 
              onClick={() => setActiveView('main')}
            >
              Back to Stock List
            </Button>
          )}
        </div>

        
        <div className="flex-1 overflow-auto">
          {activeView === 'main' ? (
            <div className="w-full">
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="py-3 px-4 text-left font-medium">Stock ID</th>
                      <th className="py-3 px-4 text-left font-medium">Product Name</th>
                      <th className="py-3 px-4 text-left font-medium">Category</th>
                      <th className="py-3 px-4 text-left font-medium">Category ID</th>
                      <th className="py-3 px-4 text-left font-medium">Stock Level</th>
                      <th className="py-3 px-4 text-left font-medium">Unit Price</th>
                      <th className="py-3 px-4 text-left font-medium">Stock Type</th>
                      
                    </tr>
                  </thead>
                  <tbody>
                    {isLoading ? (
                      <tr>
                        <td colSpan={8} className="py-4 text-center">Loading stock items...</td>
                      </tr>
                    ) : stockItems.length === 0 ? (
                      <tr>
                        <td colSpan={8} className="py-4 text-center">No stock items found</td>
                      </tr>
                    ) : (
                      stockItems.map((item) => (
                        <tr key={item.stock_id} className="border-b">
                          <td className="py-3 px-4">{item.stock_id}</td>
                          <td className="py-3 px-4">{item.item_name}</td>
                          <td className="py-3 px-4">{item.category?.category_name}</td>
                          <td className="py-3 px-4">{item.category_id}</td>
                          <td className="py-3 px-4">{item.quantity}</td>
                          <td className="py-3 px-4">Rs. {item.price}</td>
                          <td className="py-3 px-4">{item.is_main_stock ? 'Main Stock' : 'Sub Stock'}</td>
                          
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          ) : activeView === 'add-new' ? (
            <AddNewItemForm />
          ) : (
            <RestockForm />
          )}
        </div>
      </CardContent>
    </Card>
    </div>
  );
}
