import { NextResponse } from "next/server";
import { prisma } from "@/prisma";

export async function PATCH(request: Request) {
  try {
    console.log("Updating category");
    
    const { categoryId, categoryName } = await request.json();

    console.log(categoryId, categoryName);

    if ( !categoryId || typeof categoryId !== "number" || !categoryName || typeof categoryName !== "string" ) {
      return NextResponse.json({ error: "Invalid input" }, { status: 400 });
    }

    const updatedCategory = await prisma.category.update({
      where: { category_id:categoryId },
      data: { category_name: categoryName },
    });

    return NextResponse.json(
      {
        categoryId: updatedCategory.category_id,
        categoryName: updatedCategory.category_name,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Update error:", error);
    return NextResponse.json(
      { error: "Failed to update category" },
      { status: 500 }
    );
  }
}
