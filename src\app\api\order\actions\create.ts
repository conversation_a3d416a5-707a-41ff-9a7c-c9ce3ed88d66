'use server'

import { auth } from "@/auth"
import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"

export async function createOrder(data: {
  shop_id: number
  stock_id: number
  quantity: number
  status?: string
  reason?: string
}) {
  try {
    const session = await auth()
    if (!session?.user) {
      throw new Error("Unauthorized")
    }

    const order = await prisma.order.create({
      data: {
        ...data,
        status: data.status || "PENDING",
        order_date: new Date()
      },
      include: {
        shop: true,
        stock: true
      }
    })

    revalidatePath('/orders')
    return { success: true, data: order }
  } catch (error) {
    console.error("Error creating order:", error)
    return { success: false, error: "Failed to create order" }
  }
} 