import { Pencil } from "lucide-react";
import { <PERSON><PERSON> } from "../ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

interface UpdateShopProps {
    shopName:string;
    regNo:string;
    category:string;
    contact:string;
    email:string;
}
export default function UpdateShop({shopName,regNo,category,contact,email}:UpdateShopProps) {
    return (
        <>
            <Dialog>
            <DialogTrigger asChild className="cursor-pointer">
                <button className="bg-transparent cursor-pointer"><Pencil className="text-yellow-400 size-4" /></button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Update Shop</DialogTitle>
                    <DialogDescription>
                        Update a shop in your list here.
                    </DialogDescription>
                </DialogHeader>
                <form>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="shopName" className="text-right"> Shop Name </Label>
                            <Input id="shopName" placeholder="Enter your Shop Name" className="col-span-3" defaultValue={shopName} />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="username" className="text-right"> Reg No </Label>
                            <Input id="username" placeholder="Enter Shop Registration Number" className="col-span-3" defaultValue={regNo} />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="category" className="text-right"> Category </Label>
                            <Select defaultValue={category}>
                                <SelectTrigger className="w-[280px]">
                                    <SelectValue placeholder="Select Category"/>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Food">Food</SelectItem>
                                    <SelectItem value="Tech">Tech</SelectItem>
                                    <SelectItem value="Clothing">Clothing</SelectItem>
                                    <SelectItem value="Books">Books</SelectItem>
                                    <SelectItem value="Home">Home</SelectItem>
                                    <SelectItem value="Crafts">Crafts</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="contact" className="text-right"> Contact </Label>
                            <Input id="contact" placeholder="Contact number" className="col-span-3" defaultValue={contact} />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="email" className="text-right"> Email </Label>
                            <Input type="email" id="email" placeholder="Email address" className="col-span-3" defaultValue={email} />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button type="reset">Clear Form</Button>
                        <Button type="submit">Update Shop</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
        </>
    )
}