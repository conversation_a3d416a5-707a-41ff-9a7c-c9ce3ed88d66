// app/api/categories/route.ts
import { NextResponse } from "next/server";
import {prisma} from "@/prisma";

export async function GET() {
  try {
    const categories = await prisma.category.findMany();

    // Transform the data to match frontend expectations
    const transformedCategories = categories.map(category => ({
      categoryId: category.category_id,
      categoryName: category.category_name
    }));

    return NextResponse.json(transformedCategories, { status: 200 });
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
