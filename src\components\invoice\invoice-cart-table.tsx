import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { InvoiceCartColumns } from "./invoice-cart-column";
import { CustomTableMeta } from "../types/table";
import { LineItem } from "../types/invoice"; 

interface InvoiceCartTableProps<TData extends LineItem, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  calculateItemTotal: (item: LineItem) => number;
  removeItem: (id: string) => void;
  handleItemChange: (id: string, field: string, value: string | number) => void;
}

export default function InvoiceCartTable<TData extends LineItem, TValue>({
  columns,
  data,
  calculateItemTotal,
  removeItem,
  handleItemChange,
}: InvoiceCartTableProps<TData, TValue>) {
  const table = useReactTable({
    data,
    columns: InvoiceCartColumns(removeItem, handleItemChange),
    getCoreRowModel: getCoreRowModel(),
    meta: { calculateItemTotal } as CustomTableMeta<TData>,
  });

  return (
    <div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}