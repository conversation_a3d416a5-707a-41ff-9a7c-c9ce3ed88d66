import { ColumnDef } from "@tanstack/react-table";
import UpdateCategory from "./update-category";
import DeleteCategory from "./delete-category";

interface Category {
  categoryId: number;
  categoryName: string;
}

interface CategoryColumnsProps {
  onUpdate: (categoryId: number, categoryName: string) => void;
  onDelete: (categoryId: number) => void;
}

export const CategoryColumns = ({ onUpdate, onDelete }: CategoryColumnsProps): ColumnDef<Category>[] => [
    { 
        accessorKey: "categoryId", 
        header: "Category Id" 
    },
    { 
        accessorKey: "categoryName", 
        header: "Category Name" 
    },
    { 
        accessorKey: "actions", 
        cell: ({ row }) => {
            const category = row.original
       
            return (
                <div className="flex flex-row gap-4">
                    <UpdateCategory 
                        categoryId={Number(category.categoryId)} 
                        categoryName={category.categoryName}
                        onUpdate={onUpdate}
                    />
                    <DeleteCategory 
                        categoryId={Number(category.categoryId)} 
                        categoryName={category.categoryName}
                        onDelete={onDelete}
                    />
                </div>
            )
          },
      
    },
]
