'use server'

import { auth } from "@/auth"
import prisma from "@/lib/prisma"
import { revalidatePath } from "next/cache"
import { z } from "zod"

const updateShopSchema = z.object({
  name: z.string().min(1, 'Shop name is required').optional(),
  contact_number: z.string().min(1, 'Contact number is required').optional(),
  email: z.string().email('Invalid email address').optional(),
  category_id: z.number().optional(),
  route_id: z.number().optional(),
  service_area: z.string().min(1, 'Service area is required').optional(),
  registration_number: z.string().min(1, 'Registration number is required').optional(),
})

export async function updateShop(shopId: number, data: z.infer<typeof updateShopSchema>) {
  try {
    const session = await auth()
    if (!session?.user || session.user.role !== 'ADMIN') {
      throw new Error("Unauthorized")
    }

    // Validate input data
    const validatedData = updateShopSchema.parse(data)

    // Check if shop exists
    const existingShop = await prisma.shop.findUnique({
      where: { shop_id: shopId },
    })

    if (!existingShop) {
      return { success: false, error: "Shop not found" }
    }

    // If route is being updated, generate new shop code
    let shopCode = existingShop.shop_code
    if (validatedData.route_id || validatedData.service_area) {
      const route = await prisma.route.findUnique({
        where: { route_id: validatedData.route_id || existingShop.route_id },
      })

      if (!route) {
        throw new Error("Route not found")
      }

      const serviceArea = validatedData.service_area || existingShop.service_area
      shopCode = `${route.route_code}-${serviceArea.substring(0, 3).toUpperCase()}`
    }

    const updatedShop = await prisma.shop.update({
      where: { shop_id: shopId },
      data: {
        ...validatedData,
        shop_code: shopCode,
      },
      include: {
        category: true,
        route: true,
      },
    })

    revalidatePath('/shops')
    return { success: true, data: updatedShop }
  } catch (error) {
    console.error("Error updating shop:", error)
    if (error instanceof z.ZodError) {
      return { success: false, error: error.errors }
    }
    return { success: false, error: "Failed to update shop" }
  }
} 