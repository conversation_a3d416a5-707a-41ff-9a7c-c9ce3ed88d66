"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  ShoppingCart,
  Edit,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  CreditCard,
  Banknote,
  FileText,
  Plus,
  Search,
  Filter,
} from "lucide-react"

interface Order {
  id: string
  shop: string
  shopCode: string
  items: number
  total: number
  status: "delivered" | "pending" | "cancelled" | "processing"
  payment: "cash" | "credit" | "check" | "mixed"
  date: string
  deliveryDate?: string
  notes?: string
}

interface OrderItem {
  id: string
  name: string
  category: string
  quantity: number
  unitPrice: number
  total: number
}

export default function OrderManagement() {
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [filterStatus, setFilterStatus] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [cancelReason, setCancelReason] = useState("")
  const [cancelComment, setCancelComment] = useState("")

  // Sample order data
  const orders: Order[] = [
    {
      id: "ORD-001",
      shop: "Saman Store",
      shopCode: "RT01-001",
      items: 25,
      total: 15750,
      status: "delivered",
      payment: "cash",
      date: "2025-01-28",
      deliveryDate: "2025-01-28",
    },
    {
      id: "ORD-002",
      shop: "Nimal Mart",
      shopCode: "RT02-002",
      items: 18,
      total: 12300,
      status: "pending",
      payment: "credit",
      date: "2025-01-28",
    },
    {
      id: "ORD-003",
      shop: "Kamal Shop",
      shopCode: "RT01-003",
      items: 32,
      total: 18900,
      status: "cancelled",
      payment: "check",
      date: "2025-01-27",
      notes: "Customer requested cancellation",
    },
    {
      id: "ORD-004",
      shop: "Sunil Store",
      shopCode: "RT03-004",
      items: 22,
      total: 14200,
      status: "processing",
      payment: "mixed",
      date: "2025-01-28",
    },
    {
      id: "ORD-005",
      shop: "Ravi Mart",
      shopCode: "RT01-005",
      items: 15,
      total: 9800,
      status: "pending",
      payment: "cash",
      date: "2025-01-28",
    },
  ]

  // Sample order items for detailed view
  const orderItems: OrderItem[] = [
    { id: "1", name: "Coca Cola 500ml", category: "Beverages", quantity: 24, unitPrice: 120, total: 2880 },
    { id: "2", name: "Milk Powder 400g", category: "Dairy", quantity: 5, unitPrice: 850, total: 4250 },
    { id: "3", name: "Bread", category: "Bakery", quantity: 10, unitPrice: 85, total: 850 },
    { id: "4", name: "Rice 5kg", category: "Grains", quantity: 8, unitPrice: 720, total: 5760 },
  ]

  const cancellationReasons = [
    "Out of Stock",
    "Customer Request",
    "Delivery Issue",
    "Payment Issue",
    "Quality Issue",
    "Route Change",
    "Other",
  ]

  const filteredOrders = orders.filter((order) => {
    const matchesStatus = filterStatus === "all" || order.status === filterStatus
    const matchesSearch =
      order.shop.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.shopCode.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesStatus && matchesSearch
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "delivered":
        return <CheckCircle className="h-3 w-3 mr-1" />
      case "pending":
        return <Clock className="h-3 w-3 mr-1" />
      case "cancelled":
        return <XCircle className="h-3 w-3 mr-1" />
      case "processing":
        return <ShoppingCart className="h-3 w-3 mr-1" />
      default:
        return null
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "delivered":
        return "default"
      case "pending":
        return "secondary"
      case "cancelled":
        return "destructive"
      case "processing":
        return "outline"
      default:
        return "secondary"
    }
  }

  const getPaymentIcon = (payment: string) => {
    switch (payment) {
      case "cash":
        return <Banknote className="h-4 w-4" />
      case "credit":
        return <CreditCard className="h-4 w-4" />
      case "check":
        return <FileText className="h-4 w-4" />
      case "mixed":
        return <DollarSign className="h-4 w-4" />
      default:
        return null
    }
  }

  const handleProcessDelivery = (orderId: string) => {
    console.log(`Processing delivery for order: ${orderId}`)
    // Implementation for processing delivery
  }

  const handleCancelOrder = (orderId: string) => {
    console.log(`Cancelling order: ${orderId}`, { reason: cancelReason, comment: cancelComment })
    // Implementation for cancelling order
    setCancelReason("")
    setCancelComment("")
  }

  const handleEditOrder = (orderId: string) => {
    console.log(`Editing order: ${orderId}`)
    // Implementation for editing order
  }

  return (
    <div className="space-y-6">
      {/* Order Management Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Order Management</h2>
          <p className="text-muted-foreground">Handle deliveries, cancellations, and order editing</p>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              New Order
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Create New Order</DialogTitle>
              <DialogDescription>Add a new order for a registered shop</DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="orderShop">Select Shop</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose shop" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="RT01-001">Saman Store (RT01-001)</SelectItem>
                    <SelectItem value="RT02-002">Nimal Mart (RT02-002)</SelectItem>
                    <SelectItem value="RT01-003">Kamal Shop (RT01-003)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="paymentMode">Payment Mode</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="credit">Credit</SelectItem>
                    <SelectItem value="check">Check</SelectItem>
                    <SelectItem value="mixed">Mixed Payment</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="mt-4">
              <Label>Order Items</Label>
              <div className="border rounded-lg p-4 mt-2">
                <p className="text-sm text-muted-foreground">Item selection interface would be implemented here</p>
              </div>
            </div>
            <Button className="w-full mt-4">Create Order</Button>
          </DialogContent>
        </Dialog>
      </div>

      {/* Order Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orders.length}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+3</span> from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orders.filter((o) => o.status === "pending").length}</div>
            <p className="text-xs text-muted-foreground">Awaiting processing</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orders.filter((o) => o.status === "delivered").length}</div>
            <p className="text-xs text-muted-foreground">Successfully completed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Order Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              Rs. {orders.reduce((sum, order) => sum + order.total, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Total order value</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Orders</CardTitle>
          <CardDescription>Manage and track all orders</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search orders, shops, or order IDs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-48">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Orders</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Orders Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order ID</TableHead>
                <TableHead>Shop</TableHead>
                <TableHead>Shop Code</TableHead>
                <TableHead>Items</TableHead>
                <TableHead>Total</TableHead>
                <TableHead>Payment</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.id}</TableCell>
                  <TableCell>{order.shop}</TableCell>
                  <TableCell>{order.shopCode}</TableCell>
                  <TableCell>{order.items}</TableCell>
                  <TableCell>Rs. {order.total.toLocaleString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {getPaymentIcon(order.payment)}
                      <span className="capitalize">{order.payment}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusVariant(order.status)}>
                      {getStatusIcon(order.status)}
                      {order.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{order.date}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      {/* View Order Details */}
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="outline" onClick={() => setSelectedOrder(order)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl">
                          <DialogHeader>
                            <DialogTitle>Order Details - {order.id}</DialogTitle>
                            <DialogDescription>
                              Order for {order.shop} ({order.shopCode})
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label>Order Information</Label>
                                <div className="space-y-2 mt-2">
                                  <p>
                                    <strong>Order ID:</strong> {order.id}
                                  </p>
                                  <p>
                                    <strong>Shop:</strong> {order.shop}
                                  </p>
                                  <p>
                                    <strong>Shop Code:</strong> {order.shopCode}
                                  </p>
                                  <p>
                                    <strong>Order Date:</strong> {order.date}
                                  </p>
                                  <p>
                                    <strong>Status:</strong>
                                    <Badge variant={getStatusVariant(order.status)} className="ml-2">
                                      {order.status}
                                    </Badge>
                                  </p>
                                </div>
                              </div>
                              <div>
                                <Label>Payment Information</Label>
                                <div className="space-y-2 mt-2">
                                  <p>
                                    <strong>Payment Mode:</strong> {order.payment}
                                  </p>
                                  <p>
                                    <strong>Total Items:</strong> {order.items}
                                  </p>
                                  <p>
                                    <strong>Total Amount:</strong> Rs. {order.total.toLocaleString()}
                                  </p>
                                  {order.deliveryDate && (
                                    <p>
                                      <strong>Delivery Date:</strong> {order.deliveryDate}
                                    </p>
                                  )}
                                </div>
                              </div>
                            </div>

                            <div>
                              <Label>Order Items</Label>
                              <Table className="mt-2">
                                <TableHeader>
                                  <TableRow>
                                    <TableHead>Item</TableHead>
                                    <TableHead>Category</TableHead>
                                    <TableHead>Quantity</TableHead>
                                    <TableHead>Unit Price</TableHead>
                                    <TableHead>Total</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {orderItems.map((item) => (
                                    <TableRow key={item.id}>
                                      <TableCell>{item.name}</TableCell>
                                      <TableCell>{item.category}</TableCell>
                                      <TableCell>{item.quantity}</TableCell>
                                      <TableCell>Rs. {item.unitPrice}</TableCell>
                                      <TableCell>Rs. {item.total.toLocaleString()}</TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>

                            {order.notes && (
                              <div>
                                <Label>Notes</Label>
                                <p className="mt-2 p-3 bg-muted rounded-md">{order.notes}</p>
                              </div>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>

                      {/* Edit Order */}
                      {(order.status === "pending" || order.status === "processing") && (
                        <Button size="sm" variant="outline" onClick={() => handleEditOrder(order.id)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}

                      {/* Process Delivery */}
                      {order.status === "pending" && (
                        <Button size="sm" onClick={() => handleProcessDelivery(order.id)}>
                          Process
                        </Button>
                      )}

                      {/* Cancel Order */}
                      {(order.status === "pending" || order.status === "processing") && (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button size="sm" variant="destructive">
                              Cancel
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Cancel Order - {order.id}</DialogTitle>
                              <DialogDescription>Please provide a reason for cancelling this order</DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <Label>Reason for Cancellation</Label>
                                <Select value={cancelReason} onValueChange={setCancelReason}>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select reason" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {cancellationReasons.map((reason) => (
                                      <SelectItem key={reason} value={reason.toLowerCase().replace(" ", "-")}>
                                        {reason}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <Label htmlFor="cancelComment">Additional Comments</Label>
                                <Textarea
                                  id="cancelComment"
                                  placeholder="Enter additional details about the cancellation..."
                                  value={cancelComment}
                                  onChange={(e) => setCancelComment(e.target.value)}
                                />
                              </div>
                              <Button
                                className="w-full"
                                variant="destructive"
                                onClick={() => handleCancelOrder(order.id)}
                                disabled={!cancelReason}
                              >
                                Cancel Order
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredOrders.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <ShoppingCart className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No orders found matching your criteria</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
