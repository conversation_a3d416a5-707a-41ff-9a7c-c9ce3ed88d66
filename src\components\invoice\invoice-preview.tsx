import { formatCurrency, formatDate } from "@/lib/utils";
import { InvoiceData } from "../types/invoice";

interface InvoicePreviewProps {
  invoiceData: InvoiceData;
  calculateItemDiscount: (item: any) => number;
  calculateItemTotal: (item: any) => number;
  calculateTotalItemDiscounts: () => number;
  calculateSubtotal: () => number;
  calculateDiscount: () => number;
  calculateTaxableAmount: () => number;
  calculateTax: () => number;
  calculateTotal: () => number;
}

export default function InvoicePreview({
  invoiceData,
  calculateItemDiscount,
  calculateItemTotal,
  calculateTotalItemDiscounts,
  calculateSubtotal,
  calculateDiscount,
  calculateTaxableAmount,
  calculateTax,
  calculateTotal,
}: InvoicePreviewProps) {
  return (
    <div className="bg-white text-black p-8 min-h-[29.7cm] w-full" style={{ backgroundColor: '#ffffff' }}>
      <div className="mb-8">
        {/* First row: Logo and Company Details */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-start gap-4">
            {invoiceData.companyLogo && (
              <div className="h-16 w-16 relative">
                <img
                  src="/logo.png"
                  alt="Company Logo"
                  className="h-full w-full object-contain"
                />
              </div>
            )}
          </div>
          <div className="text-right" style={{ color: '#000000' }}>
            {invoiceData.companyName && <p className="font-bold">{invoiceData.companyName}</p>}
            {invoiceData.companyDetails && (
              <p className="text-sm" style={{ color: '#4b5563' }}>{invoiceData.companyDetails}</p>
            )}
          </div>
        </div>

        {/* Separator line */}
        <div className="w-full h-px my-6" style={{ backgroundColor: '#e5e7eb' }}></div>

        {/* Second row: Invoice and Dates */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold mb-1" style={{ color: '#000000' }}>INVOICE</h1>
            <p style={{ color: '#4b5563' }}>#{invoiceData.invoiceNumber}</p>
          </div>
          <div className="text-right" style={{ color: '#000000' }}>
            <p>Date: {formatDate(invoiceData.date)}</p>
            <p>Due Date: {formatDate(invoiceData.dueDate)}</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-8 mb-8">
        <div>
          <h2 className="text-lg font-semibold mb-2" style={{ color: '#000000' }}>From:</h2>
          <div style={{ color: '#4b5563' }}>
            <p className="font-semibold">{invoiceData.fromName}</p>
            <p>{invoiceData.fromEmail}</p>
            <p className="whitespace-pre-line">{invoiceData.fromAddress}</p>
          </div>
        </div>
        <div className="mt-4 sm:mt-0">
          <h2 className="text-lg font-semibold mb-2" style={{ color: '#000000' }}>To:</h2>
          <div style={{ color: '#4b5563' }}>
            <p className="font-semibold">{invoiceData.toName}</p>
            <p>{invoiceData.toEmail}</p>
            <p className="whitespace-pre-line">{invoiceData.toAddress}</p>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto mb-8">
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b-2" style={{ borderColor: '#d1d5db' }}>
              <th className="py-2 text-left" style={{ color: '#000000' }}>Description</th>
              <th className="py-2 text-right" style={{ color: '#000000' }}>Quantity</th>
              <th className="py-2 text-right" style={{ color: '#000000' }}>Price</th>
              {invoiceData.lineItems.some((item) => item.currency !== invoiceData.currency) && (
                <th className="py-2 text-right" style={{ color: '#000000' }}>Currency</th>
              )}
              <th className="py-2 text-right" style={{ color: '#000000' }}>Discount</th>
              <th className="py-2 text-right" style={{ color: '#000000' }}>Amount</th>
            </tr>
          </thead>
          <tbody>
            {invoiceData.lineItems.map((item) => (
              <tr key={item.itemCode} className="border-b" style={{ borderColor: '#e5e7eb' }}>
                <td className="py-3" style={{ color: '#000000' }}>{item.itemName}</td>
                <td className="py-3 text-right" style={{ color: '#000000' }}>{item.quantity}</td>
                <td className="py-3 text-right" style={{ color: '#000000' }}>
                  {formatCurrency(item.price, item.currency)}
                </td>
                {invoiceData.lineItems.some((item) => item.currency !== invoiceData.currency) && (
                  <td className="py-3 text-right" style={{ color: '#000000' }}>
                    {item.currency}
                    {item.currency !== invoiceData.currency && (
                      <span className="text-xs block" style={{ color: '#6b7280' }}>
                        Rate: {item.exchangeRate}
                      </span>
                    )}
                  </td>
                )}
                <td className="py-3 text-right" style={{ color: '#000000' }}>
                  {item.discountValue > 0 ? (
                    <span className="font-medium">
                      {item.discountType === "percentage"
                        ? `${item.discountValue}%`
                        : formatCurrency(item.discountValue, item.currency)}
                    </span>
                  ) : (
                    "-"
                  )}
                </td>
                <td className="py-3 text-right" style={{ color: '#000000' }}>
                  {item.currency !== invoiceData.currency ? (
                    <>
                      <span className="text-xs block" style={{ color: '#6b7280' }}>
                        {formatCurrency(item.quantity * item.price - calculateItemDiscount(item), item.currency)}
                      </span>
                      {formatCurrency(calculateItemTotal(item), invoiceData.currency)}
                    </>
                  ) : (
                    formatCurrency(calculateItemTotal(item), invoiceData.currency)
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-end mb-8">
        <div className="w-full sm:w-64">
          <div className="flex justify-between py-2" style={{ color: '#000000' }}>
            <span>Subtotal:</span>
            <span>{formatCurrency(calculateSubtotal(), invoiceData.currency)}</span>
          </div>

          {calculateTotalItemDiscounts() > 0 && (
            <div className="flex justify-between py-2 font-medium" style={{ color: '#000000' }}>
              <span>Item Discounts:</span>
              <span>-{formatCurrency(calculateTotalItemDiscounts(), invoiceData.currency)}</span>
            </div>
          )}

          {invoiceData.discountValue > 0 && (
            <div className="flex justify-between py-2 font-medium" style={{ color: '#000000' }}>
              <span>
                Invoice Discount {invoiceData.discountType === "percentage" ? `(${invoiceData.discountValue}%)` : ""}:
                {!invoiceData.applyInvoiceDiscountToDiscountedItems && (
                  <span className="text-xs block" style={{ color: '#6b7280' }}>
                    (Applied only to non-discounted items)
                  </span>
                )}
              </span>
              <span>-{formatCurrency(calculateDiscount(), invoiceData.currency)}</span>
            </div>
          )}

          <div className="flex justify-between py-2 border-b" style={{ borderColor: '#e5e7eb', color: '#000000' }}>
            <span>Tax ({invoiceData.taxRate}%):</span>
            <span>{formatCurrency(calculateTax(), invoiceData.currency)}</span>
          </div>

          <div className="flex justify-between py-2 font-bold text-lg" style={{ color: '#000000' }}>
            <span>Total:</span>
            <span>{formatCurrency(calculateTotal(), invoiceData.currency)}</span>
          </div>
        </div>
      </div>

      {invoiceData.notes && (
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-2" style={{ color: '#000000' }}>Notes:</h2>
          <p className="whitespace-pre-line" style={{ color: '#4b5563' }}>{invoiceData.notes}</p>
        </div>
      )}

      <div className="text-center text-sm mt-16 border-t pt-4" style={{ borderColor: '#e5e7eb', color: '#6b7280' }}>
        <p className="whitespace-pre-line">{invoiceData.footer}</p>
      </div>
    </div>
  );
}