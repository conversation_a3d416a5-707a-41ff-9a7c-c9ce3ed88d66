import { NextResponse } from "next/server";
import {prisma} from "@/prisma";

export async function POST(request: Request) {
    try {
        const { categoryName } = await request.json();
  
      if (!categoryName || typeof categoryName !== "string") {
        return NextResponse.json({ error: "Invalid category name" }, { status: 400 });
      }
  
      const newCategory = await prisma.category.create({
        data: {
          category_name: categoryName,
        },
      });
  
      return NextResponse.json({
        categoryId: newCategory.category_id,
        categoryName: newCategory.category_name,
      }, { status: 201 });
    } catch (error) {
      return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
    }
}
  