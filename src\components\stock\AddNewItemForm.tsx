'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select';


interface Category {
  category_id: number;
  category_name: string;
}

export default function AddNewItemForm() {
  const [productName, setProductName] = useState('');
  const [category, setCategory] = useState('');
  const [quantity, setQuantity] = useState('');
  const [unitPrice, setUnitPrice] = useState('');
  const [dateAdded, setDateAdded] = useState('');
  const [isMainStock, setIsMainStock] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Validation function
  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    // Product Name validation - required and no numbers
    if (!productName.trim()) {
      newErrors.productName = 'Product name is required';
    } else if (/\d/.test(productName)) {
      newErrors.productName = 'Product name should not contain numbers';
    }
    
    // Category validation
    if (!category) {
      newErrors.category = 'Category is required';
    }
    
    // Quantity validation - required and must be a positive number
    if (!quantity) {
      newErrors.quantity = 'Quantity is required';
    } else if (isNaN(Number(quantity)) || Number(quantity) <= 0) {
      newErrors.quantity = 'Quantity must be a positive number';
    }
    
    // Unit Price validation - required and must be a positive number
    if (!unitPrice) {
      newErrors.unitPrice = 'Unit price is required';
    } else if (isNaN(Number(unitPrice)) || Number(unitPrice) <= 0) {
      newErrors.unitPrice = 'Unit price must be a positive number';
    }
    
    // Date validation
    if (!dateAdded) {
      newErrors.dateAdded = 'Date is required';
    }
    
    // Stock Type validation
    if (!isMainStock) {
      newErrors.isMainStock = 'Stock type is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle input changes with validation
  const handleProductNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Only allow letters and spaces (no numbers or special characters)
    if (value === '' || /^[A-Za-z\s]+$/.test(value)) {
      setProductName(value);
      // Clear error when user starts typing
      if (errors.productName) {
        setErrors(prev => ({ ...prev, productName: '' }));
      }
    }
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow positive numbers
    if (value === '' || (!isNaN(Number(value)) && Number(value) >= 0)) {
      setQuantity(value);
      if (errors.quantity) {
        setErrors(prev => ({ ...prev, quantity: '' }));
      }
    }
  };

  const handleUnitPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow positive numbers
    if (value === '' || (!isNaN(Number(value)) && Number(value) >= 0)) {
      setUnitPrice(value);
      if (errors.unitPrice) {
        setErrors(prev => ({ ...prev, unitPrice: '' }));
      }
    }
  };

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories/create');
        if (response.ok) {
          const data = await response.json();
          setCategories(data);
        }
      } catch (error) {
        console.error('Failed to fetch categories:', error);
      }
    };

    fetchCategories();
  }, []);


  const handleSubmit = async () => {
    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      const res = await fetch('/api/stock/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productName,
          category,
          quantity,
          unitPrice,
          dateAdded,
          isMainStock,
        }),
      });

      const data = await res.json();

      if (!res.ok) {
        throw new Error(data.error || 'Failed to add stock item');
      }

      // Success - reset form and show success message
      setProductName('');
      setCategory('');
      setQuantity('');
      setUnitPrice('');
      setDateAdded('');
      setIsMainStock('');
      setErrors({});
      alert('Stock item added successfully!');

    } catch (error) {
      console.error('Error adding stock item:', error);
      alert(error instanceof Error ? error.message : 'Failed to add stock item');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex justify-center pt-6">
      <div className="w-full max-w-2xl p-8 border rounded-xl shadow-md flex flex-col justify-between">
        <div className="m-4">
          <h2 className="text-xl font-semibold mb-3">Add New Stock Item</h2>
          <p className="text-sm text-muted-foreground mb-6">
            Enter details for the new inventory item.
          </p>

          <div className="grid gap-4">
            <div>
              <Label htmlFor="name">Product Name</Label>
              <div className='mt-2'>
              <Input
                id="name"
                value={productName}
                onChange={handleProductNameChange}
                className={errors.productName ? 'border-red-500' : ''}
              />
              {errors.productName && <p className="text-red-500 text-sm mt-1">{errors.productName}</p>}
              </div>
            </div>

            <div>
              <Label htmlFor="category">Category</Label>
              <div className='mt-2'>
              <Select 
                value={category} 
                onValueChange={(value) => {
                  setCategory(value);
                  if (errors.category) setErrors(prev => ({ ...prev, category: '' }));
                }}
              >
                <SelectTrigger id="category" className={`w-full ${errors.category ? 'border-red-500' : ''}`}>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((cat) => (
                    <SelectItem key={cat.category_id} value={cat.category_name}>
                      {cat.category_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.category && <p className="text-red-500 text-sm mt-1">{errors.category}</p>}
              </div>
            </div>

            <div>
              <Label htmlFor="quantity">Quantity</Label>
              <div className='mt-2'>
              <Input
                id="quantity"
                type="number"
                value={quantity}
                onChange={handleQuantityChange}
                className={errors.quantity ? 'border-red-500' : ''}
                min="0"
              />
              {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>}
              </div>
            </div>

            <div>
              <Label htmlFor="price">Unit Price</Label>
              <div className='mt-2'>
              <Input
                id="price"
                type="number"
                value={unitPrice}
                onChange={handleUnitPriceChange}
                className={errors.unitPrice ? 'border-red-500' : ''}
                min="0"
                step="0.01"
              />
              {errors.unitPrice && <p className="text-red-500 text-sm mt-1">{errors.unitPrice}</p>}
              </div>
            </div>
            <div>
                <Label htmlFor="date" className="flex items-center gap-2">Date</Label>
                <div className='mt-2'>
                  <Input 
                    id="date" 
                    type="date" 
                    value={dateAdded} 
                    onChange={(e) => {
                      setDateAdded(e.target.value);
                      if (errors.dateAdded) setErrors(prev => ({ ...prev, dateAdded: '' }));
                    }} 
                    className={errors.dateAdded ? 'border-red-500' : ''}
                  />
                  {errors.dateAdded && <p className="text-red-500 text-sm mt-1">{errors.dateAdded}</p>}
                </div>
            </div>
            
             <div>
                <Label htmlFor="stockType" className="flex items-center gap-2">Stock Type</Label>
                <div className='mt-2'>
                  <Select 
                    value={isMainStock} 
                    onValueChange={(value) => {
                      setIsMainStock(value);
                      if (errors.isMainStock) setErrors(prev => ({ ...prev, isMainStock: '' }));
                    }}
                  >
                    <SelectTrigger id="stockType" className={`w-full ${errors.isMainStock ? 'border-red-500' : ''}`}>
                      <SelectValue placeholder="Select stock type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Main Stock">Main Stock</SelectItem>
                      <SelectItem value="Sub Stock">Sub Stock</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.isMainStock && <p className="text-red-500 text-sm mt-1">{errors.isMainStock}</p>}
                </div>
              </div>
          </div>

          <div className="mt-10 flex justify-between gap-4">

            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? 'Saving...' : 'Save Item'}
            </Button>

          </div>
        </div>
      </div>
    </div>
  );
}
