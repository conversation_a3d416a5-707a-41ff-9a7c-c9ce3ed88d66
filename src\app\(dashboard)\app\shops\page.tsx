"use client";

import AddShop from "@/components/shops/add-shop";
import { shopColumns } from "@/components/shops/shop-columns";
import { ShopsTable } from "@/components/shops/shops-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";



export default function ShopsPage() {

    const [searchTerm, setSearchTerm] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const shopsPerPage = 16;

    const filteredShops = shops.filter(shop =>
        shop.shopName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.contact.includes(searchTerm) ||
        shop.shopCode.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const totalPages = Math.ceil(filteredShops.length / shopsPerPage);

    const currentShops = filteredShops.slice((currentPage - 1) * shopsPerPage, currentPage * shopsPerPage);

    const handleChangePage = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
        }
    };

    return (
        <div className="space-y-4">
            <div className="flex flex-row gap-6 items-center justify-between">
                
                <div className="flex flex-row gap-6 items-center">
                    <AddShop />
                </div>
            </div>
            <Input
                type="text"
                placeholder="Search shops..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-1/3"
            />
            <div>
                <ShopsTable data={currentShops} columns={shopColumns}></ShopsTable>
                <div className="flex items-center justify-end space-x-2 py-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleChangePage(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        Previous
                    </Button>
                    <span>Page {currentPage} of {totalPages}</span>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleChangePage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Next
                    </Button>
                </div>
            </div>
        </div>
    )
}