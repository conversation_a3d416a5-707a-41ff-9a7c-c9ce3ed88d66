"use client";

import AddShop from "@/components/shops/add-shop";
import { createShopColumns } from "@/components/shops/shop-columns";
import { ShopsTable } from "@/components/shops/shops-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState, useEffect } from "react";
import { getShops } from "@/app/api/shops/actions/read";
import { toast } from "sonner";

interface Shop {
    shop_id: number;
    shop_code: string;
    name: string;
    service_area: string;
    registration_number: string;
    contact_number?: string;
    email?: string;
    route_id: number;
    category_id: number;
    category: {
        category_id: number;
        category_name: string;
    };
    route: {
        route_id: number;
        route_code: string;
        description: string;
    };
    _count: {
        orders: number;
    };
}

export default function ShopsPage() {
    const [searchTerm, setSearchTerm] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [shops, setShops] = useState<Shop[]>([]);
    const [loading, setLoading] = useState(true);
    const shopsPerPage = 16;

    useEffect(() => {
        fetchShops();
    }, []);

    const fetchShops = async () => {
        try {
            setLoading(true);
            const result = await getShops();
            if (result.success && result.data) {
                setShops(result.data);
            } else {
                toast.error(result.error || "Failed to fetch shops");
            }
        } catch (error) {
            toast.error("Error fetching shops");
            console.error("Error fetching shops:", error);
        } finally {
            setLoading(false);
        }
    };

    const filteredShops = shops.filter(shop =>
        shop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.category.category_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.shop_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.route.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        shop.registration_number.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const totalPages = Math.ceil(filteredShops.length / shopsPerPage);

    const currentShops = filteredShops.slice((currentPage - 1) * shopsPerPage, currentPage * shopsPerPage);

    const handleChangePage = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
        }
    };

    if (loading) {
        return (
            <div className="space-y-4">
                <div className="flex items-center justify-center py-8">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                        <p className="mt-2 text-sm text-gray-600">Loading shops...</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="flex flex-row gap-6 items-center justify-between">
                <div className="flex flex-row gap-6 items-center">
                    <AddShop onShopAdded={fetchShops} />
                </div>
            </div>
            <Input
                type="text"
                placeholder="Search shops..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-1/3"
            />
            <div>
                <ShopsTable data={currentShops} columns={createShopColumns(fetchShops)}></ShopsTable>
                <div className="flex items-center justify-end space-x-2 py-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleChangePage(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        Previous
                    </Button>
                    <span>Page {currentPage} of {totalPages}</span>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleChangePage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Next
                    </Button>
                </div>
            </div>
        </div>
    )
}