import { useEffect, useState } from "react";
import { InvoiceData, LineItem } from "../types/invoice";
import { toast } from "sonner";
import { Card, CardContent } from "../ui/card";
import { Textarea } from "../ui/textarea";
import { Label } from "../ui/label";
import { Button } from "../ui/button";
import { Check, Minus, Plus, Trash2 } from "lucide-react";
import { Input } from "../ui/input";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Command, CommandInput, CommandItem, CommandList } from "../ui/command";
import { cn, currencies, formatCurrency } from "@/lib/utils";
import { Checkbox } from "../ui/checkbox";
import { number, set, string } from "zod";
import InvoiceCartTable from "./invoice-cart-table";
import { InvoiceCartColumns } from "./invoice-cart-column";

interface InvoiceFormProps {
    invoiceData: InvoiceData;
    setInvoiceData: React.Dispatch<React.SetStateAction<InvoiceData>>;
    handleInvoiceChange: (field: string, value: string | number | boolean) => void;
    handleItemChange: (id: string, field: string, value: string | number) => void;
    handleLogoUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
    // addItem: () => void;
    removeItem: (id: string) => void;
    calculateItemDiscount: (item: any) => number;
    calculateItemTotal: (item: any) => number;
    calculateTotalItemDiscounts: () => number;
    calculateSubtotal: () => number;
    calculateDiscount: () => number;
    calculateTaxableAmount: () => number;
    calculateTax: () => number;
    calculateTotal: () => number;
}

export function InvoiceForm({ invoiceData, setInvoiceData, handleInvoiceChange, handleItemChange, handleLogoUpload, removeItem, calculateItemDiscount, calculateItemTotal,
    calculateTotalItemDiscounts, calculateSubtotal, calculateDiscount, calculateTax, calculateTaxableAmount, calculateTotal, }: InvoiceFormProps) {

    // const [companyData, setCompanyData] = useState({ name: "", details: "", contactEmail: "", contactAddress: "" });
    const [customers, setCustomers] = useState<any[]>([]);
    const [open, setOpen] = useState(false);
    const [openItemList, setOpenItemList] = useState(false);
    const [selectedItem, setSelectedItem] = useState<LineItem>({
        itemCode: "",
        itemName: "",
        category: "",
        quantity: 0,
        price: 0,
        currency: "LKR",
        exchangeRate: 0,
        discountType: "percentage",
        discountValue: 0,
    });

    const shops = [
        {
            id: "1",
            shopCode: "KAN01UP23",
            regNumber: "*********",
            shopName: "Your Tech",
            category: "Tech",
            contact: "0710000000",
            email: "<EMAIL>",
            createdAt: "2023-01-01",
        },
        {
            id: "2",
            shopCode: "DEL02MH24",
            regNumber: "*********",
            shopName: "Gadget Hub",
            category: "Tech",
            contact: "0*********",
            email: "<EMAIL>",
            createdAt: "2024-03-15",
        },
        {
            id: "3",
            shopCode: "MUM03GJ23",
            regNumber: "456789123",
            shopName: "Fashion Corner",
            category: "Clothing",
            contact: "**********",
            email: "<EMAIL>",
            createdAt: "2023-06-22",
        },
        {
            id: "4",
            shopCode: "BAN04KA24",
            regNumber: "321654987",
            shopName: "Book Nook",
            category: "Books",
            contact: "**********",
            email: "<EMAIL>",
            createdAt: "2024-01-10",
        },
        {
            id: "5",
            shopCode: "CHE05TN23",
            regNumber: "654987321",
            shopName: "Healthy Bites",
            category: "Food",
            contact: "**********",
            email: "<EMAIL>",
            createdAt: "2023-11-05",
        },
        {
            id: "6",
            shopCode: "HYD06TG24",
            regNumber: "789123456",
            shopName: "Tech Trend",
            category: "Tech",
            contact: "**********",
            email: "<EMAIL>",
            createdAt: "2024-07-19",
        },
        {
            id: "7",
            shopCode: "PUN07MH23",
            regNumber: "147258369",
            shopName: "Style Studio",
            category: "Clothing",
            contact: "**********",
            email: "<EMAIL>",
            createdAt: "2023-09-12",
        },
        {
            id: "8",
            shopCode: "KOL08WB24",
            regNumber: "258369147",
            shopName: "Gourmet Delights",
            category: "Food",
            contact: "**********",
            email: "<EMAIL>",
            createdAt: "2024-02-28",
        },
        {
            id: "9",
            shopCode: "AHM09GJ23",
            regNumber: "369147258",
            shopName: "Home Essentials",
            category: "Home",
            contact: "0854321098",
            email: "<EMAIL>",
            createdAt: "2023-04-17",
        },
        {
            id: "10",
            shopCode: "JAI10RJ24",
            regNumber: "741852963",
            shopName: "Crafty Corner",
            category: "Crafts",
            contact: "0743210987",
            email: "<EMAIL>",
            createdAt: "2024-05-03",
        }
    ]

    const items = [
        {
            itemCode: 'ITM001',
            itemName: 'Kiwi',
            category: 'Fruit',
            price: 120,
            createdAt: '2025-05-28'
        },
        {
            itemCode: 'ITM002',
            itemName: 'Lemon',
            category: 'Fruit',
            price: 190,
            createdAt: '2025-05-28'
        },
        {
            itemCode: 'ITM003',
            itemName: 'Mango',
            category: 'Fruit',
            price: 240,
            createdAt: '2025-05-28'
        },
        {
            itemCode: 'ITM004',
            itemName: 'Nectarine',
            category: 'Fruit',
            price: 2500,
            createdAt: '2025-05-28'
        },
        {
            itemCode: 'ITM005',
            itemName: 'Orange',
            category: 'Fruit',
            price: 50,
            createdAt: '2025-05-28'
        },
        {
            itemCode: 'ITM006',
            itemName: 'Papaya',
            category: 'Fruit',
            price: 100,
            createdAt: '2025-05-28'
        },
        {
            itemCode: 'ITM007',
            itemName: 'Quince',
            category: 'Fruit',
            price: 150,
            createdAt: '2025-05-28'
        },
        {
            itemCode: 'ITM008',
            itemName: 'Raspberry',
            category: 'Fruit',
            price: 200,
            createdAt: '2025-05-28'
        },
        {
            itemCode: 'ITM009',
            itemName: 'Strawberry',
            category: 'Fruit',
            price: 270,
            createdAt: '2025-05-28'
        },
        {
            itemCode: 'ITM010',
            itemName: 'Tangerine',
            category: 'Fruit',
            price: 500,
            createdAt: '2025-05-28'
        },
    ]


    // useEffect(() => {
    //     const fetchCustomers = async () => {
    //         try {
    //             const res = await fetch("/api/customer");
    //             const data = await res.json();
    //             if (!res.ok) {
    //                 toast.error(data.error || "Failed to fetch customers");
    //             } else {
    //                 setCustomers(data.customers);
    //             }
    //         } catch (error) {
    //             console.error(error);
    //             toast.error("Something went wrong! Please try again.");
    //         }
    //     };
    //     fetchCustomers();
    // }, []);

    // useEffect(() => {
    //     const fetchCompanyData = async () => {
    //         try {
    //             const res = await fetch("/api/company");
    //             const data = await res.json();

    //             if (res.ok && data.companies.length > 0) {
    //                 const company = data.companies[0];

    //                 // Update local company data state
    //                 setCompanyData({
    //                     name: company.name || "",
    //                     details: company.details || "",
    //                     contactEmail: company.contactEmail || "",
    //                     contactAddress: company.contactAddress || "",
    //                 });

    //                 console.log("Company Data:", company);

    //                 // Ensure handleInvoiceChange is called for all fields correctly
    //                 handleInvoiceChange("companyName", company.name);
    //                 handleInvoiceChange("companyDetails", company.details);
    //                 handleInvoiceChange("fromAddress", company.contactAddress);
    //                 handleInvoiceChange("fromEmail", company.contactEmail);
    //                 handleInvoiceChange("fromName", company.name);
    //             } else {
    //                 toast.error("No company data found.");
    //             }
    //         } catch (error) {
    //             console.error("Error fetching company data:", error);
    //             toast.error("Failed to fetch company details.");
    //         }
    //     };

    //     fetchCompanyData();
    // }, []);

    function addItem() {
        if (invoiceData.lineItems.find((item) => item.itemCode === selectedItem.itemCode)) {
            toast.error("Item already added.");
            return;
        }
        if (!selectedItem.itemCode || !selectedItem.itemName) {
            toast.error("Please select an item.");
            return;
        }
        setInvoiceData((prevData) => ({
            ...prevData,
            lineItems: [...prevData.lineItems, { ...selectedItem }],
        }));
        unSelectItem();
    }


    function unSelectItem() {
        setSelectedItem((prevItem) => ({
            ...prevItem,
            itemCode: "",
            itemName: "",
            category: "",
            quantity: 0,
            price: 0,
            currency: "LKR",
            exchangeRate: 0,
            discountType: "percentage",
            discountValue: 0,
        }));
    }


    function onSelectItem(itemCode: string, itemName: string, category: string, price: number) {
        setSelectedItem((prevItem) => ({ ...prevItem, itemCode, itemName, category, price }));
    }
    return (
        <Card>
            <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <h2 className="text-xl font-semibold mb-4">Invoice Details</h2>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="invoiceNumber">Invoice Number</Label>
                                <Input id="invoiceNumber" value={invoiceData.invoiceNumber} onChange={(e) => handleInvoiceChange("invoiceNumber", e.target.value)} />
                            </div>
                            <div>
                                <Label htmlFor="date">Date</Label>
                                <Input id="date" type="date" value={invoiceData.date} onChange={(e) => handleInvoiceChange("date", e.target.value)} />
                            </div>
                            <div>
                                <Label htmlFor="dueDate">Due Date</Label>
                                <Input id="dueDate" type="date" value={invoiceData.dueDate} onChange={(e) => handleInvoiceChange("dueDate", e.target.value)} />
                            </div>
                            <div>
                                <Label htmlFor="currency">Invoice Currency</Label>
                                <Select value={invoiceData.currency} onValueChange={(value) => handleInvoiceChange("currency", value)} >
                                    <SelectTrigger id="currency">
                                        <SelectValue placeholder="Select currency" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {currencies.map((currency) => (
                                            <SelectItem key={currency.code} value={currency.code}>
                                                {currency.code} - {currency.name}
                                            </SelectItem>))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="taxRate">Tax Rate (%)</Label>
                                <Input id="taxRate" type="number" min="0" max="100" value={invoiceData.taxRate} onChange={(e) => handleInvoiceChange("taxRate", Number.parseFloat(e.target.value) || 0)} />
                            </div>
                            <div>
                                <Label>Invoice Discount</Label>
                                <div className="space-y-4 mt-2">
                                    <RadioGroup value={invoiceData.discountType} onValueChange={(value) => handleInvoiceChange("discountType", value as "percentage" | "amount")} className="flex items-center gap-6">
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="percentage" id="percentage" />
                                            <Label htmlFor="percentage" className="cursor-pointer">
                                                Percentage (%)
                                            </Label>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <RadioGroupItem value="amount" id="amount" />
                                            <Label htmlFor="amount" className="cursor-pointer"> Amount </Label>
                                        </div>
                                    </RadioGroup>
                                    <Input type="number" min="0" step={invoiceData.discountType === "percentage" ? "0.01" : "0.01"} value={invoiceData.discountValue} onChange={(e) => handleInvoiceChange("discountValue", Number.parseFloat(e.target.value) || 0)} placeholder={invoiceData.discountType === "percentage" ? "%" : invoiceData.currency} className="w-full" />
                                    <div className="flex items-center space-x-2">
                                        <Checkbox id="applyInvoiceDiscountToDiscountedItems" checked={invoiceData.applyInvoiceDiscountToDiscountedItems} onCheckedChange={(checked) => handleInvoiceChange("applyInvoiceDiscountToDiscountedItems", checked === true)} />
                                        <Label htmlFor="applyInvoiceDiscountToDiscountedItems" className="text-sm cursor-pointer" > Apply invoice discount to already discounted items </Label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="space-y-4">
                        <h2 className="text-xl font-semibold">To</h2>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="toName">Name</Label>
                                <Popover open={open} onOpenChange={setOpen}>
                                    <PopoverTrigger asChild className="w-full">
                                        <Button variant="outline" role="combobox" aria-expanded={open} > {invoiceData.toName || "Select customer"} </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-[500px] p-0">
                                        <Command>
                                            <CommandInput placeholder="Search customer..." />
                                            <CommandList >
                                                {shops.length === 0 && (<p className="p-2 text-sm text-muted-foreground">
                                                    No shops found
                                                </p>)}
                                                {shops.map((shop) => (
                                                    <CommandItem
                                                        key={shop.id}
                                                        value={shop.shopName}
                                                        onSelect={() => {
                                                            handleInvoiceChange("toEmail", shop.email ?? "");
                                                            handleInvoiceChange("toAddress", shop.category ?? "");
                                                            handleInvoiceChange("toName", shop.shopName ?? "");
                                                            setOpen(false);
                                                        }}
                                                    >
                                                        <Check className={cn("mr-2 h-4 w-4", invoiceData.toName === shop.shopName ? "opacity-100" : "opacity-0")} />
                                                        {shop.shopName}
                                                    </CommandItem>
                                                ))}
                                            </CommandList>
                                        </Command>
                                    </PopoverContent>
                                </Popover>
                            </div>
                            <div>
                                <Label htmlFor="toEmail">Email</Label>
                                <Input id="toEmail" type="email" value={invoiceData.toEmail} onChange={(e) => handleInvoiceChange("toEmail", e.target.value)} />
                            </div>
                            <div className="md:col-span-2">
                                <Label htmlFor="toAddress">Address</Label>
                                <Textarea id="toAddress" value={invoiceData.toAddress} onChange={(e) => handleInvoiceChange("toAddress", e.target.value)} />
                            </div>
                        </div>
                    </div>
                </div>

                <div className="space-y-6">
                    <div className="border rounded-md p-4 flex flex-col gap-4">
                        <div className="grid grid-cols-12 gap-4">
                            <div className="sm:col-span-12 flex flex-col gap-2">
                                <h2 className="text-xl font-semibold mb-4">Items</h2>
                                <Popover open={openItemList} onOpenChange={setOpenItemList}>
                                    <PopoverTrigger asChild className="w-lg">
                                        <Button variant="outline" role="combobox" aria-expanded={openItemList}> Select an item </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-[500px] p-0">
                                        <Command>
                                            <CommandInput placeholder="Search item..." />
                                            <CommandList>
                                                {items.length === 0 && (
                                                    <p className="p-2 text-sm text-muted-foreground">No items found</p>
                                                )}
                                                {items.map((item) => (
                                                    <CommandItem
                                                        key={item.itemCode}
                                                        value={item.itemName}
                                                        onSelect={() => {
                                                            onSelectItem(item.itemCode, item.itemName, item.category, item.price);
                                                            setOpenItemList(false);
                                                        }}
                                                    >
                                                        {item.itemCode} - {item.itemName}
                                                    </CommandItem>
                                                ))}
                                            </CommandList>
                                        </Command>
                                    </PopoverContent>
                                </Popover>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4">
                            <div className="sm:col-span-2 flex flex-col gap-2">
                                <Label htmlFor="itemCode">Item Code</Label>
                                <Input id="itemCode" value={selectedItem.itemCode} readOnly />
                            </div>
                            <div className="sm:col-span-5 flex flex-col gap-2">
                                <Label htmlFor="itemName">Item Name</Label>
                                <Input id="itemName" value={selectedItem.itemName} readOnly />
                            </div>
                            <div className="sm:col-span-2 flex flex-col gap-2">
                                <Label htmlFor="quantity">Quantity</Label>
                                <Input id="quantity" type="number" min="1" value={selectedItem.quantity}
                                    onChange={(e) => setSelectedItem((prev) => ({ ...prev, quantity: Number(e.target.value) }))}
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4">
                            <div className="sm:col-span-2 flex flex-col gap-2">
                                <Label htmlFor="price">Price</Label>
                                <Input id="price" type="number" min="0" step="0.01" value={selectedItem.price} readOnly />
                            </div>
                            <div className="sm:col-span-2 flex flex-col gap-4">
                                <Label>Discount Type:</Label>
                                <RadioGroup value={selectedItem.discountType} className="flex items-center gap-4"
                                    onValueChange={(value) =>
                                        setSelectedItem((prev) => ({ ...prev, discountType: value as "percentage" | "amount" }))
                                    }
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="percentage" id="percentage" />
                                        <Label htmlFor="percentage" className="cursor-pointer">
                                            Percentage (%)
                                        </Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="amount" id="amount" />
                                        <Label htmlFor="amount" className="cursor-pointer">Amount</Label>
                                    </div>
                                </RadioGroup>
                            </div>
                            <div className="sm:col-span-2 flex flex-col gap-2">
                                <Label htmlFor="discountValue">Discount Value:</Label>
                                <Input id="discountValue" type="number" min="0" step={selectedItem.discountType === "percentage" ? "0.01" : "0.01"} value={selectedItem.discountValue} placeholder={selectedItem.discountType === "percentage" ? "%" : selectedItem.currency} className="w-full"
                                    onChange={(e) =>
                                        setSelectedItem((prev) => ({ ...prev, discountValue: Number(e.target.value) }))
                                    }
                                />
                            </div>
                            <div className="sm:col-span-1 flex flex-col gap-2">
                                <Label htmlFor="currency">Currency</Label>
                                <Select value={selectedItem.currency}
                                    onValueChange={(value) => setSelectedItem((prev) => ({ ...prev, currency: value }))}
                                >
                                    <SelectTrigger id="currency" className="w-full">
                                        <SelectValue placeholder="Select currency" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {currencies.map((currency) => (
                                            <SelectItem key={currency.code} value={currency.code}>
                                                {currency.code}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            {selectedItem.currency !== invoiceData.currency && (
                                <div className="sm:col-span-2 flex flex-col gap-2">
                                    <Label htmlFor={`exchangeRate-${selectedItem.itemCode}`} className="flex items-center gap-1">
                                        Exchange Rate
                                        <span className="text-xs text-muted-foreground whitespace-nowrap">
                                            (1 {selectedItem.currency} = ? {invoiceData.currency})
                                        </span>
                                    </Label>
                                    <Input id="exchangeRate" type="number" min="0.000001" step="0.000001" value={selectedItem.exchangeRate}
                                        onChange={(e) =>
                                            setSelectedItem((prev) => ({ ...prev, exchangeRate: Number(e.target.value) }))
                                        }
                                    />
                                </div>
                            )}
                            <div className={selectedItem.currency !== invoiceData.currency ? "col-span-2 flex flex-col" : "col-span-4"}>
                                <div className="text-right text-sm text-muted-foreground">
                                    {selectedItem.currency !== invoiceData.currency ? (<>
                                        {selectedItem.quantity} ×{" "}
                                        {formatCurrency(selectedItem.price, selectedItem.currency)} ={" "}
                                        {formatCurrency(selectedItem.quantity * selectedItem.price, selectedItem.currency)}
                                        {selectedItem.discountValue > 0 && (<>
                                            <br />
                                            <span className="text-foreground font-medium">
                                                Discount: - {formatCurrency(calculateItemDiscount(selectedItem), selectedItem.currency)}
                                            </span>
                                        </>)}
                                        <br />
                                        <span className="font-medium text-foreground">
                                            {formatCurrency(calculateItemTotal(selectedItem), invoiceData.currency)}
                                        </span>
                                    </>) : (<>
                                        {selectedItem.quantity} ×{" "}
                                        {formatCurrency(selectedItem.price, selectedItem.currency)} = {" "}
                                        {formatCurrency(selectedItem.quantity * selectedItem.price, selectedItem.currency)}
                                        {selectedItem.discountValue > 0 && (<>
                                            <br />
                                            <span className="text-foreground font-medium">
                                                Discount: - {formatCurrency(calculateItemDiscount(selectedItem), selectedItem.currency)}
                                            </span>
                                        </>)}
                                        <br />
                                        <span className="font-medium text-foreground">
                                            {formatCurrency(calculateItemTotal(selectedItem), invoiceData.currency)}
                                        </span>
                                    </>)}
                                </div>
                            </div>
                        </div>
                        <div className="grid grid-cols-12 gap-4">
                            <div className="sm:col-span-6 flex flex-col gap-2">
                                <Button variant="outline" onClick={unSelectItem} className="flex items-center mt-4" > <Minus className="h-4 w-4 mr-2" /> Clear Form </Button>
                            </div>
                            <div className="sm:col-span-6 flex flex-col gap-2">
                                <Button variant="outline" onClick={addItem} className="flex items-center mt-4" > <Plus className="h-4 w-4 mr-2" /> Add Item </Button>
                            </div>
                        </div>
                    </div>
                </div>

                <InvoiceCartTable
                    data={invoiceData.lineItems}
                    columns={InvoiceCartColumns(removeItem, handleItemChange)}
                    calculateItemTotal={calculateItemTotal}
                    removeItem={removeItem}
                    handleItemChange={handleItemChange}
                />
                <div className="mt-6 border-t pt-4">
                    <div className="flex flex-col gap-2 sm:w-72 ml-auto">
                        <div className="flex justify-between">
                            <span>Subtotal:</span>
                            <span className="min-w-[100px] text-right">
                                {formatCurrency(calculateSubtotal(), invoiceData.currency)}
                            </span>
                        </div>

                        {calculateTotalItemDiscounts() > 0 && (
                            <div className="flex justify-between text-white font-medium">
                                <span>Item Discounts:</span>
                                <span className="min-w-[100px] text-right">
                                    -
                                    {formatCurrency(calculateTotalItemDiscounts(), invoiceData.currency)}
                                </span>
                            </div>)}

                        {invoiceData.discountValue > 0 && (<div className="flex justify-between text-foreground font-medium">
                            <span>
                                Invoice Discount{" "}
                                {invoiceData.discountType === "percentage" ? `(${invoiceData.discountValue}%)` : ""}
                                :
                                {!invoiceData.applyInvoiceDiscountToDiscountedItems && (
                                    <span className="text-xs block text-muted-foreground">
                                        (Applied only to non-discounted items)
                                    </span>)}
                            </span>
                            <span className="min-w-[100px] text-right">
                                -{formatCurrency(calculateDiscount(), invoiceData.currency)}
                            </span>
                        </div>)}

                        <div className="flex justify-between">
                            <span>Tax ({invoiceData.taxRate}%):</span>
                            <span className="min-w-[100px] text-right">
                                {formatCurrency(calculateTax(), invoiceData.currency)}
                            </span>
                        </div>
                        <div className="flex justify-between font-bold border-t pt-2 mt-2">
                            <span>Total:</span>
                            <span className="min-w-[100px] text-right">
                                {formatCurrency(calculateTotal(), invoiceData.currency)}
                            </span>
                        </div>
                    </div>
                </div>

                <div className="mt-6">
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea id="notes" value={invoiceData.notes} onChange={(e) => handleInvoiceChange("notes", e.target.value)} placeholder="Payment terms, bank details, etc." />
                </div>

                <div className="mt-6">
                    <Label htmlFor="footer">Invoice Footer</Label>
                    <Textarea id="footer" value={invoiceData.footer} onChange={(e) => handleInvoiceChange("footer", e.target.value)} placeholder="Company information, website, thank you message, etc." />
                </div>
            </CardContent>
        </Card >
    );
}