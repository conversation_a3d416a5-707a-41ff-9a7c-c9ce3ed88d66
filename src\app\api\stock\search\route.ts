import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();
export async function GET(req: NextRequest) {  try {
    const searchParams = req.nextUrl.searchParams;    const itemName = searchParams.get('item_name');
    if (!itemName) {
      return NextResponse.json({ error: 'item_name parameter is required' }, { status: 400 });    }
    const stockItems = await prisma.stock.findMany({
      where: {
        item_name: itemName, // Exact match instead of contains
      },
      include: {
        category: true, // Include category details
      },
    });
    if (stockItems.length === 0) {
      return NextResponse.json({ message: 'No items found' }, { status: 404 });
    }
    return NextResponse.json(stockItems, { status: 200 });
  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'Failed to search stock items', details: String(error) },
      { status: 500 }
    );
  }
}



















