import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, Trash2, Pencil } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { CustomTableMeta } from "../types/table";
import { LineItem } from "../types/invoice"; 

export const InvoiceCartColumns = (
  removeItem: (id: string) => void,
  handleItemChange: (id: string, field: string, value: string | number) => void
): ColumnDef<LineItem>[] => [
  { accessorKey: "itemCode", header: "Item Code" },
  { accessorKey: "itemName", header: "Item Name" },
  { accessorKey: "category", header: "Category" },
  {
    accessorKey: "price",
    header: () => <div>Price</div>,
    cell: ({ row }) => {
      const item = row.original;
      return (
        <div className=" font-medium">
          {formatCurrency(item.price, item.currency)}
        </div>
      );
    },
  },
  {
    accessorKey: "quantity",
    header: "Quantity",
    cell: ({ row }) => {
      const item = row.original;
      return (
        <div>
          <input type="number" min="1" value={item.quantity}
            onChange={(e) =>
              handleItemChange(item.itemCode, "quantity", Number(e.target.value))
            }
            className="w-16 border rounded p-1"
          />
        </div>
      );
    },
  },
  {
    accessorKey: "discountType",
    header: "Discount Type",
    cell: ({ row }) => {
      const item = row.original;
      return <div>{item.discountType === "percentage" ? "%" : "Amount"}</div>;
    },
  },
  {
    accessorKey: "discountValue",
    header: "Discount Value",
    cell: ({ row }) => {
      const item = row.original;
      return (
        <div>
          {item.discountValue > 0
            ? item.discountType === "percentage"
              ? `${item.discountValue}%`
              : formatCurrency(item.discountValue, item.currency)
            : "-"}
        </div>
      );
    },
  },
  {
    accessorKey: "itemTotal",
    header: () => <div>Item Total</div>,
    cell: ({ row, table }) => {
      const item = row.original;
      const calculateItemTotal = (table.options.meta as CustomTableMeta<LineItem>)?.calculateItemTotal;
      return (
        <div className="font-medium">
          {formatCurrency(calculateItemTotal(item), item.currency)}
        </div>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const item = row.original;
      return (
        <div className="flex flex-row gap-4 justify-end">
          <button className="bg-transparent cursor-pointer" onClick={() => handleItemChange(item.itemCode, "edit", item.itemCode)} >
            <Pencil className="text-yellow-400 size-4" />
          </button>
          <button className="bg-transparent cursor-pointer" onClick={() => removeItem(item.itemCode)} >
            <Trash2 className="text-red-400 size-4" />
          </button>
        </div>
      );
    },
  },
];