"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { DataTable } from "@/components/item/item-table";
import { columns } from "@/components/item/columns";
import { Button } from "@/components/ui/button";

const items = [
    {
        itemCode: 'ITM001',
        itemName: 'Kiwi',
        category: 'Fruit',
        price: 1.2,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM002',
        itemName: 'Lemon',
        category: 'Fruit',
        price: 1.9,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM003',
        itemName: 'Mango',
        category: 'Fruit',
        price: 2.4,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM004',
        itemName: 'Nectarine',
        category: 'Fruit',
        price: 2.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM005',
        itemName: 'Orange',
        category: 'Fruit',
        price: 0.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM006',
        itemName: 'Papaya',
        category: 'Fruit',
        price: 1.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM007',
        itemName: 'Quince',
        category: 'Fruit',
        price: 1.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM008',
        itemName: 'Raspberry',
        category: 'Fruit',
        price: 2.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM009',
        itemName: 'Strawberry',
        category: 'Fruit',
        price: 2.7,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM010',
        itemName: 'Tangerine',
        category: 'Fruit',
        price: 0.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM011',
        itemName: 'Kale',
        category: 'Vegetable',
        price: 1.2,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM012',
        itemName: 'Leek',
        category: 'Vegetable',
        price: 1.7,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM013',
        itemName: 'Mushroom',
        category: 'Vegetable',
        price: 2.4,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM014',
        itemName: 'Napa Cabbage',
        category: 'Vegetable',
        price: 2.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM015',
        itemName: 'Okra',
        category: 'Vegetable',
        price: 0.7,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM016',
        itemName: 'Parsnip',
        category: 'Vegetable',
        price: 1.2,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM017',
        itemName: 'Radish',
        category: 'Vegetable',
        price: 1.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM018',
        itemName: 'Spinach',
        category: 'Vegetable',
        price: 2.2,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM019',
        itemName: 'Turnip',
        category: 'Vegetable',
        price: 2.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM020',
        itemName: 'Ube',
        category: 'Vegetable',
        price: 0.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM021',
        itemName: 'Vidalia Onion',
        category: 'Vegetable',
        price: 1.2,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM022',
        itemName: 'Watercress',
        category: 'Vegetable',
        price: 1.7,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM023',
        itemName: 'Yam',
        category: 'Vegetable',
        price: 2.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM024',
        itemName: 'KitKat',
        category: 'Snack',
        price: 2.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM025',
        itemName: 'Lollipop',
        category: 'Snack',
        price: 0.9,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM026',
        itemName: 'Muffin',
        category: 'Snack',
        price: 1.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM027',
        itemName: 'Nachos',
        category: 'Snack',
        price: 1.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM028',
        itemName: 'Oreos',
        category: 'Snack',
        price: 2.4,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM029',
        itemName: 'Popcorn',
        category: 'Snack',
        price: 2.7,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM030',
        itemName: 'Quavers',
        category: 'Snack',
        price: 0.7,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM031',
        itemName: 'Rice Cake',
        category: 'Snack',
        price: 1.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM032',
        itemName: 'Trail Mix',
        category: 'Snack',
        price: 1.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM033',
        itemName: 'Milk',
        category: 'Dairy',
        price: 2.2,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM034',
        itemName: 'Yogurt',
        category: 'Dairy',
        price: 2.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM035',
        itemName: 'Cheese',
        category: 'Dairy',
        price: 0.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM036',
        itemName: 'Butter',
        category: 'Dairy',
        price: 1.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM037',
        itemName: 'Cream',
        category: 'Dairy',
        price: 1.9,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM038',
        itemName: 'Juice',
        category: 'Beverage',
        price: 2.4,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM039',
        itemName: 'Soda',
        category: 'Beverage',
        price: 2.7,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM040',
        itemName: 'Tea',
        category: 'Beverage',
        price: 0.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM041',
        itemName: 'Coffee',
        category: 'Beverage',
        price: 1.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM042',
        itemName: 'Water',
        category: 'Beverage',
        price: 1.9,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM043',
        itemName: 'Zucchini',
        category: 'Vegetable',
        price: 1.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM037',
        itemName: 'Cream',
        category: 'Dairy',
        price: 1.9,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM038',
        itemName: 'Juice',
        category: 'Beverage',
        price: 2.4,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM039',
        itemName: 'Soda',
        category: 'Beverage',
        price: 2.7,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM040',
        itemName: 'Tea',
        category: 'Beverage',
        price: 0.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM041',
        itemName: 'Coffee',
        category: 'Beverage',
        price: 1.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM042',
        itemName: 'Water',
        category: 'Beverage',
        price: 1.9,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM043',
        itemName: 'Zucchini',
        category: 'Vegetable',
        price: 1.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM041',
        itemName: 'Coffee',
        category: 'Beverage',
        price: 1.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM042',
        itemName: 'Water',
        category: 'Beverage',
        price: 1.9,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM043',
        itemName: 'Zucchini',
        category: 'Vegetable',
        price: 1.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM037',
        itemName: 'Cream',
        category: 'Dairy',
        price: 1.9,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM038',
        itemName: 'Juice',
        category: 'Beverage',
        price: 2.4,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM039',
        itemName: 'Soda',
        category: 'Beverage',
        price: 2.7,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM040',
        itemName: 'Tea',
        category: 'Beverage',
        price: 0.5,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM041',
        itemName: 'Coffee',
        category: 'Beverage',
        price: 1.0,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM042',
        itemName: 'Water',
        category: 'Beverage',
        price: 1.9,
        createdAt: '2025-05-28'
    },
    {
        itemCode: 'ITM043',
        itemName: 'Zucchini',
        category: 'Vegetable',
        price: 1.5,
        createdAt: '2025-05-28'
    }

]
export default function ItemsPage() {
    const [searchTerm, setSearchTerm] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 16;

    const filteredItems = items.filter(item =>
        item.itemName.toLowerCase().includes(searchTerm.toLowerCase())
    );
    const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
    
    const currentItems = filteredItems.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

    const handleChangePage = (newPage: number) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
        }
    };

    return (
        <div className="space-y-4">
            <div className="flex flex-row gap-6 items-center">
                <Input
                    type="text"
                    placeholder="Search items..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-1/3"
                />
            </div>
            <div>
                <div>
                    <DataTable columns={columns} data={currentItems} />
                </div>
            </div>
            <div className="flex items-center justify-end space-x-2 py-4">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleChangePage(currentPage - 1)}
                    disabled={currentPage === 1}
                >
                    Previous
                </Button>
                <span>Page {currentPage} of {totalPages}</span>
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleChangePage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                >
                    Next
                </Button>
            </div>
        </div>
    )
}