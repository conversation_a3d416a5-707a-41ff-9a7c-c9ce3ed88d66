import { Badge } from "@/components/ui/badge"
import { CheckCircle, Clock, XCircle, ShoppingCart } from "lucide-react"

interface OrderStatusBadgeProps {
  status: "delivered" | "pending" | "cancelled" | "processing"
}

export function OrderStatusBadge({ status }: OrderStatusBadgeProps) {
  const getStatusIcon = () => {
    switch (status) {
      case "delivered":
        return <CheckCircle className="h-3 w-3 mr-1" />
      case "pending":
        return <Clock className="h-3 w-3 mr-1" />
      case "cancelled":
        return <XCircle className="h-3 w-3 mr-1" />
      case "processing":
        return <ShoppingCart className="h-3 w-3 mr-1" />
      default:
        return null
    }
  }

  const getStatusVariant = () => {
    switch (status) {
      case "delivered":
        return "default"
      case "pending":
        return "secondary"
      case "cancelled":
        return "destructive"
      case "processing":
        return "outline"
      default:
        return "secondary"
    }
  }

  return (
    <Badge variant={getStatusVariant()}>
      {getStatusIcon()}
      {status}
    </Badge>
  )
}
